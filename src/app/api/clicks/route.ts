import { NextRequest, NextResponse } from 'next/server'
import { getUserFromRequest } from '@/lib/auth'
import { getMissionById, updateMission, createClickLog, updateUserStats, getDatabase } from '@/lib/database'
import { validateClick } from '@/lib/missionUtils'

interface ClickRequest {
  missionId?: string
  clickData: {
    x: number
    y: number
    timestamp: number
  }
  clickPower?: number
}

// POST /api/clicks - Process a click for a mission
export async function POST(request: NextRequest) {
  try {
    // Get user from auth
    const user = await getUserFromRequest(request)

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = user.id
    const body: ClickRequest = await request.json()

    if (!body.clickData) {
      return NextResponse.json({ error: 'Missing click data' }, { status: 400 })
    }

    const clickPower = body.clickPower || 1

    // Handle mission-based clicks
    if (body.missionId) {
      // Get the mission
      const mission = getMissionById(body.missionId)

      if (!mission || mission.user_id !== userId) {
        return NextResponse.json({ error: 'Mission not found' }, { status: 404 })
      }

      return await processMissionClick(userId, mission, body.clickData, clickPower)
    } else {
      // Handle free clicks (no mission)
      return await processFreeClick(userId, body.clickData, clickPower)
    }
  } catch (error) {
    console.error('Error processing click:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

async function processMissionClick(userId: string, mission: any, clickData: any, clickPower: number) {
  try {
    // Anti-cheat: Check click timing (prevent too rapid clicks)
    const now = Date.now()
    const clickTime = clickData.timestamp
    const timeDiff = Math.abs(now - clickTime)

    if (timeDiff > 5000) { // Click timestamp too old (>5 seconds)
      return NextResponse.json({ error: 'Invalid click timestamp' }, { status: 400 })
    }

    // Log the click for anti-cheat analysis
    try {
      createClickLog({
        user_id: userId,
        mission_id: mission.id,
        click_x: clickData.x,
        click_y: clickData.y,
        click_timestamp: new Date(clickTime).toISOString()
      })
    } catch (logError) {
      console.warn('Failed to log click:', logError)
      // Continue processing even if logging fails
    }

    // Update mission progress with click power
    const newCompletedClicks = mission.completed_clicks + clickPower
    const isCompleted = newCompletedClicks >= mission.target_clicks
    const isExceeded = newCompletedClicks > mission.target_clicks

    let scoreAwarded = 0
    let newStatus = mission.status

    if (isCompleted) {
      // Mission completed - award full score regardless of excess
      scoreAwarded = mission.score_reward
      newStatus = 'completed'

      // Apply penalty if exceeded target (but still complete the mission)
      if (isExceeded) {
        scoreAwarded -= 100 // Penalty for exceeding target
      }
    }

    // Update the mission in database
    const updatedMission = updateMission(mission.id, {
      completed_clicks: newCompletedClicks,
      status: newStatus,
      score_awarded: mission.score_awarded + scoreAwarded,
      completed_at: isCompleted ? new Date().toISOString() : null
    })

    // Update user stats
    updateUserStats(userId, {
      total_score: scoreAwarded + clickPower, // Base merit + mission bonus
      total_clicks: clickPower,
      missions_completed: isCompleted ? 1 : 0
    })

    // Get updated user stats for response
    const db = getDatabase()
    const userStats = db.prepare(`
      SELECT total_score, total_clicks, missions_completed, missions_failed
      FROM users WHERE id = ?
    `).get(userId) as any

    return NextResponse.json({
      success: true,
      mission: updatedMission,
      scoreAwarded: scoreAwarded + clickPower,
      totalScore: userStats?.total_score || 0,
      totalClicks: userStats?.total_clicks || 0
    })

  } catch (error) {
    console.error('Error processing mission click:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

async function processFreeClick(userId: string, clickData: any, clickPower: number) {
  try {
    // Anti-cheat: Check click timing
    const now = Date.now()
    const clickTime = clickData.timestamp
    const timeDiff = Math.abs(now - clickTime)

    if (timeDiff > 5000) {
      return NextResponse.json({ error: 'Invalid click timestamp' }, { status: 400 })
    }

    // Log the click
    try {
      createClickLog({
        user_id: userId,
        mission_id: null,
        click_x: clickData.x,
        click_y: clickData.y,
        click_timestamp: new Date(clickTime).toISOString()
      })
    } catch (error) {
      console.error('Error logging click:', error)
    }

    // Award merit for free clicks
    updateUserStats(userId, {
      total_score: clickPower,
      total_clicks: clickPower
    })

    // Get updated user stats
    const db = getDatabase()
    const userStats = db.prepare(`
      SELECT total_score, total_clicks
      FROM users WHERE id = ?
    `).get(userId) as any

    return NextResponse.json({
      success: true,
      scoreAwarded: clickPower,
      totalScore: userStats?.total_score || 0,
      totalClicks: userStats?.total_clicks || 0
    })

  } catch (error) {
    console.error('Error processing free click:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// GET /api/clicks - Get click statistics (for debugging)
export async function GET(request: NextRequest) {
  try {
    // Get user from auth
    const user = await getUserFromRequest(request)

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = user.id
    const url = new URL(request.url)
    const missionId = url.searchParams.get('missionId')

    if (!missionId) {
      return NextResponse.json({ error: 'Mission ID required' }, { status: 400 })
    }

    // Get click logs for the mission
    const db = getDatabase()
    const clickLogs = db.prepare(`
      SELECT * FROM click_logs
      WHERE user_id = ? AND mission_id = ?
      ORDER BY created_at DESC
    `).all(userId, missionId)

    return NextResponse.json({
      clickLogs: clickLogs || [],
      totalClicks: clickLogs?.length || 0
    })

  } catch (error) {
    console.error('Click GET API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
