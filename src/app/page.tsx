'use client'

import { useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import GameLayout from '@/components/GameLayout'
import AuthForm from '@/components/AuthForm'
import SetupNotice from '@/components/SetupNotice'
import WoodenFish from '@/components/WoodenFish'

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!supabase) {
      setLoading(false)
      return
    }

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  // Show setup notice if Supabase is not configured
  if (!supabase) {
    return <SetupNotice />
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🪵🐟</div>
          <div className="text-lg text-amber-800 font-medium">Loading...</div>
        </div>
      </div>
    )
  }

  if (!user) {
    return <AuthForm />
  }

  return (
    <GameLayout user={user}>
      <div className="flex-1 flex flex-col items-center justify-center p-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-amber-800 mb-2">
            木魚功德
          </h1>
          <p className="text-lg text-amber-600">
            Wooden Fish Merit Game
          </p>
        </div>

        {/* Placeholder for the wooden fish component */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-amber-200">
          <div className="text-center">
            <div className="text-8xl mb-4 cursor-pointer hover:scale-105 transition-transform">
              🪵🐟
            </div>
            <p className="text-amber-700 font-medium">
              Click the wooden fish to gain merit!
            </p>
            <p className="text-sm text-amber-600 mt-2">
              (Wooden fish component will be implemented next)
            </p>
          </div>
        </div>

        {/* Placeholder for missions and leaderboard */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-4xl">
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-amber-200">
            <h3 className="text-lg font-semibold text-amber-800 mb-4">
              Current Missions
            </h3>
            <p className="text-amber-600">
              Mission system will be implemented next
            </p>
          </div>

          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-amber-200">
            <h3 className="text-lg font-semibold text-amber-800 mb-4">
              Leaderboard
            </h3>
            <p className="text-amber-600">
              Leaderboard will be implemented next
            </p>
          </div>
        </div>
      </div>
    </GameLayout>
  )
}
