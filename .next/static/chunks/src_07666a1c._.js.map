{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''\n\n// Check if Supabase is configured\nconst isSupabaseConfigured = supabaseUrl && supabaseAnonKey &&\n  supabaseUrl !== 'your_supabase_project_url' &&\n  supabaseAnonKey !== 'your_supabase_anon_key'\n\n// Client-side Supabase client\nexport const supabase = isSupabaseConfigured\n  ? createBrowserClient(supabaseUrl, supabaseAnonKey)\n  : null\n\n// Server-side Supabase client (for API routes)\nexport const createServerClient = () => {\n  return createClient(\n    supabaseUrl,\n    process.env.SUPABASE_SERVICE_ROLE_KEY!\n  )\n}\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  username: string\n  clan: string\n  total_score: number\n  login_streak: number\n  missions_completed: number\n  created_at: string\n  last_login: string\n}\n\nexport interface Mission {\n  id: string\n  user_id: string\n  mission_type: 'hourly'\n  target_clicks: number\n  completed_clicks: number\n  status: 'active' | 'completed' | 'failed'\n  created_at: string\n  expires_at: string\n  score_awarded: number\n}\n\nexport interface UserStats {\n  user_id: string\n  total_score: number\n  missions_completed: number\n  login_streak: number\n  last_mission_completed: string\n  updated_at: string\n}\n\n// Hong Kong Districts for clan selection\nexport const HK_DISTRICTS = [\n  'Central and Western',\n  'Eastern',\n  'Southern', \n  'Wan Chai',\n  'Sham Shui Po',\n  'Kowloon City',\n  'Kwun Tong',\n  'Wong Tai Sin',\n  'Yau Tsim Mong',\n  'Islands',\n  'Kwai Tsing',\n  'North',\n  'Sai Kung',\n  'Sha Tin',\n  'Tai Po',\n  'Tsuen Wan',\n  'Tuen Mun',\n  'Yuen Long'\n] as const\n\nexport type HKDistrict = typeof HK_DISTRICTS[number]\n"], "names": [], "mappings": ";;;;;AAGoB;AAHpB;AACA;;;AAEA,MAAM,cAAc,iEAAwC;AAC5D,MAAM,kBAAkB,8DAA6C;AAErE,kCAAkC;AAClC,MAAM,uBAAuB,eAAe,mBAC1C,gBAAgB,+BAChB,oBAAoB;AAGf,MAAM,WAAW,6EAEpB;AAGG,MAAM,qBAAqB;IAChC,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAChB,aACA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;AAEzC;AAqCO,MAAM,eAAe;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/lib/supabase-utils.ts"], "sourcesContent": ["import { supabase } from './supabase'\nimport type { User, Mission, HKDistrict } from './supabase'\n\n// Auth utilities\nexport const signUp = async (email: string, password: string, username: string, clan: HKDistrict) => {\n  const { data, error } = await supabase.auth.signUp({\n    email,\n    password,\n    options: {\n      data: {\n        username,\n        clan\n      }\n    }\n  })\n  return { data, error }\n}\n\nexport const signIn = async (email: string, password: string) => {\n  const { data, error } = await supabase.auth.signInWithPassword({\n    email,\n    password\n  })\n  return { data, error }\n}\n\nexport const signOut = async () => {\n  const { error } = await supabase.auth.signOut()\n  return { error }\n}\n\nexport const getCurrentUser = async () => {\n  const { data: { user }, error } = await supabase.auth.getUser()\n  return { user, error }\n}\n\n// User utilities\nexport const getUserProfile = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('users')\n    .select('*')\n    .eq('id', userId)\n    .single()\n  \n  return { data, error }\n}\n\nexport const updateUserProfile = async (userId: string, updates: Partial<User>) => {\n  const { data, error } = await supabase\n    .from('users')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single()\n  \n  return { data, error }\n}\n\nexport const updateLoginStreak = async (userId: string) => {\n  const { data, error } = await supabase\n    .rpc('update_login_streak', { user_uuid: userId })\n  \n  return { data, error }\n}\n\n// Mission utilities\nexport const getUserActiveMissions = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('missions')\n    .select('*')\n    .eq('user_id', userId)\n    .eq('status', 'active')\n    .gt('expires_at', new Date().toISOString())\n    .order('created_at', { ascending: false })\n  \n  return { data, error }\n}\n\nexport const createMission = async (userId: string, targetClicks: number, expiresAt: string) => {\n  const { data, error } = await supabase\n    .from('missions')\n    .insert({\n      user_id: userId,\n      target_clicks: targetClicks,\n      expires_at: expiresAt\n    })\n    .select()\n    .single()\n  \n  return { data, error }\n}\n\nexport const updateMissionProgress = async (missionId: string, completedClicks: number) => {\n  const { data, error } = await supabase\n    .from('missions')\n    .update({ \n      completed_clicks: completedClicks,\n      updated_at: new Date().toISOString()\n    })\n    .eq('id', missionId)\n    .select()\n    .single()\n  \n  return { data, error }\n}\n\nexport const completeMission = async (missionId: string, scoreAwarded: number) => {\n  const { data, error } = await supabase\n    .from('missions')\n    .update({ \n      status: 'completed',\n      score_awarded: scoreAwarded,\n      updated_at: new Date().toISOString()\n    })\n    .eq('id', missionId)\n    .select()\n    .single()\n  \n  return { data, error }\n}\n\nexport const failMission = async (missionId: string, penaltyScore: number) => {\n  const { data, error } = await supabase\n    .from('missions')\n    .update({ \n      status: 'failed',\n      score_awarded: penaltyScore,\n      updated_at: new Date().toISOString()\n    })\n    .eq('id', missionId)\n    .select()\n    .single()\n  \n  return { data, error }\n}\n\n// Click logging for anti-cheat\nexport const logClick = async (userId: string, missionId: string, ipAddress?: string, userAgent?: string) => {\n  const { data, error } = await supabase\n    .from('click_logs')\n    .insert({\n      user_id: userId,\n      mission_id: missionId,\n      ip_address: ipAddress,\n      user_agent: userAgent\n    })\n  \n  return { data, error }\n}\n\n// Leaderboard utilities\nexport const getGlobalLeaderboard = async (limit: number = 10) => {\n  const { data, error } = await supabase\n    .from('leaderboard')\n    .select('*')\n    .limit(limit)\n  \n  return { data, error }\n}\n\nexport const getClanLeaderboard = async (clan: string, limit: number = 10) => {\n  const { data, error } = await supabase\n    .from('leaderboard')\n    .select('*')\n    .eq('clan', clan)\n    .limit(limit)\n  \n  return { data, error }\n}\n\nexport const getUserRank = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('leaderboard')\n    .select('global_rank, clan_rank')\n    .eq('id', userId)\n    .single()\n  \n  return { data, error }\n}\n\n// Mission generation utilities\nexport const shouldGenerateMissions = () => {\n  const now = new Date()\n  const hour = now.getHours()\n  \n  // No missions between 23:00 - 07:59\n  return hour >= 8 && hour <= 22\n}\n\nexport const generateMissionTargets = (): number[] => {\n  // Generate 3 random targets between 10-60 clicks\n  const targets = []\n  for (let i = 0; i < 3; i++) {\n    targets.push(Math.floor(Math.random() * 51) + 10) // 10-60 clicks\n  }\n  return targets.sort((a, b) => a - b) // Sort ascending\n}\n\nexport const getMissionExpiryTime = (): string => {\n  const now = new Date()\n  const expiry = new Date(now.getTime() + 60 * 60 * 1000) // 1 hour from now\n  return expiry.toISOString()\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;AAIO,MAAM,SAAS,OAAO,OAAe,UAAkB,UAAkB;IAC9E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;QACjD;QACA;QACA,SAAS;YACP,MAAM;gBACJ;gBACA;YACF;QACF;IACF;IACA,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,SAAS,OAAO,OAAe;IAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;QAC7D;QACA;IACF;IACA,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,UAAU;IACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7C,OAAO;QAAE;IAAM;AACjB;AAEO,MAAM,iBAAiB;IAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7D,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,oBAAoB,OAAO,QAAgB;IACtD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,oBAAoB,OAAO;IACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,GAAG,CAAC,uBAAuB;QAAE,WAAW;IAAO;IAElD,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,wBAAwB,OAAO;IAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,UAAU,UACb,EAAE,CAAC,cAAc,IAAI,OAAO,WAAW,IACvC,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,gBAAgB,OAAO,QAAgB,cAAsB;IACxE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;QACN,SAAS;QACT,eAAe;QACf,YAAY;IACd,GACC,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,wBAAwB,OAAO,WAAmB;IAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;QACN,kBAAkB;QAClB,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,WACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,kBAAkB,OAAO,WAAmB;IACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;QACN,QAAQ;QACR,eAAe;QACf,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,WACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,cAAc,OAAO,WAAmB;IACnD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;QACN,QAAQ;QACR,eAAe;QACf,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,WACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,WAAW,OAAO,QAAgB,WAAmB,WAAoB;IACpF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC;QACN,SAAS;QACT,YAAY;QACZ,YAAY;QACZ,YAAY;IACd;IAEF,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,uBAAuB,OAAO,QAAgB,EAAE;IAC3D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,qBAAqB,OAAO,MAAc,QAAgB,EAAE;IACvE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,MACX,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,cAAc,OAAO;IAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,0BACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,yBAAyB;IACpC,MAAM,MAAM,IAAI;IAChB,MAAM,OAAO,IAAI,QAAQ;IAEzB,oCAAoC;IACpC,OAAO,QAAQ,KAAK,QAAQ;AAC9B;AAEO,MAAM,yBAAyB;IACpC,iDAAiD;IACjD,MAAM,UAAU,EAAE;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,QAAQ,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI,eAAe;;IACnE;IACA,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,iBAAiB;;AACxD;AAEO,MAAM,uBAAuB;IAClC,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,MAAM,kBAAkB;;IAC1E,OAAO,OAAO,WAAW;AAC3B", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\nimport { signOut } from '@/lib/supabase-utils'\nimport { LogOut, Trophy, User as UserIcon } from 'lucide-react'\n\ninterface NavigationProps {\n  user: User | null\n}\n\nexport default function Navigation({ user }: NavigationProps) {\n  const [userProfile, setUserProfile] = useState<any>(null)\n  const [loading, setLoading] = useState(false)\n\n  useEffect(() => {\n    if (user) {\n      fetchUserProfile()\n    }\n  }, [user])\n\n  const fetchUserProfile = async () => {\n    if (!user) return\n    \n    const { data, error } = await supabase\n      .from('users')\n      .select('*')\n      .eq('id', user.id)\n      .single()\n    \n    if (data) {\n      setUserProfile(data)\n    }\n  }\n\n  const handleSignOut = async () => {\n    setLoading(true)\n    await signOut()\n    setLoading(false)\n  }\n\n  return (\n    <nav className=\"bg-white/80 backdrop-blur-sm border-b border-amber-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"text-2xl\">🪵🐟</div>\n            <div className=\"font-bold text-amber-800\">\n              <span className=\"hidden sm:inline\">木魚功德</span>\n              <span className=\"sm:hidden\">木魚</span>\n            </div>\n          </div>\n\n          {/* User Info */}\n          {user && userProfile ? (\n            <div className=\"flex items-center space-x-4\">\n              {/* Score Display */}\n              <div className=\"hidden sm:flex items-center space-x-2 bg-amber-100 px-3 py-1 rounded-full\">\n                <Trophy className=\"w-4 h-4 text-amber-600\" />\n                <span className=\"text-sm font-medium text-amber-800\">\n                  {userProfile.total_score}\n                </span>\n              </div>\n\n              {/* Clan Badge */}\n              <div className=\"hidden md:flex items-center space-x-1 bg-blue-100 px-2 py-1 rounded-full\">\n                <span className=\"text-xs font-medium text-blue-800\">\n                  {userProfile.clan}\n                </span>\n              </div>\n\n              {/* User Menu */}\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"flex items-center space-x-1\">\n                  <UserIcon className=\"w-4 h-4 text-gray-600\" />\n                  <span className=\"text-sm font-medium text-gray-700 hidden sm:inline\">\n                    {userProfile.username}\n                  </span>\n                </div>\n                \n                <button\n                  onClick={handleSignOut}\n                  disabled={loading}\n                  className=\"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors\"\n                  title=\"Sign Out\"\n                >\n                  <LogOut className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-sm text-gray-600\">\n              Welcome to 木魚功德\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Mobile Score Display */}\n      {user && userProfile && (\n        <div className=\"sm:hidden bg-amber-50 px-4 py-2 border-t border-amber-200\">\n          <div className=\"flex justify-between items-center text-sm\">\n            <div className=\"flex items-center space-x-2\">\n              <Trophy className=\"w-4 h-4 text-amber-600\" />\n              <span className=\"font-medium text-amber-800\">\n                Score: {userProfile.total_score}\n              </span>\n            </div>\n            <div className=\"text-blue-800 font-medium\">\n              {userProfile.clan}\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAAA;AAAA;;;AANA;;;;;AAYe,SAAS,WAAW,EAAE,IAAI,EAAmB;;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,MAAM;gBACR;YACF;QACF;+BAAG;QAAC;KAAK;IAET,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM;QAEX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,MAAM;YACR,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB;QACpB,WAAW;QACX,MAAM,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;QACZ,WAAW;IACb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAW;;;;;;8CAC1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,6LAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;;;;;;;wBAK/B,QAAQ,4BACP,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDACb,YAAY,WAAW;;;;;;;;;;;;8CAK5B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDACb,YAAY,IAAI;;;;;;;;;;;8CAKrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAK,WAAU;8DACb,YAAY,QAAQ;;;;;;;;;;;;sDAIzB,6LAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;iDAKxB,6LAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAQ5C,QAAQ,6BACP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAK,WAAU;;wCAA6B;wCACnC,YAAY,WAAW;;;;;;;;;;;;;sCAGnC,6LAAC;4BAAI,WAAU;sCACZ,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAO/B;GA1GwB;KAAA", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/components/GameLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode } from 'react'\nimport Navigation from './Navigation'\nimport { User } from '@supabase/supabase-js'\n\ninterface GameLayoutProps {\n  children: ReactNode\n  user: User | null\n}\n\nexport default function GameLayout({ children, user }: GameLayoutProps) {\n  return (\n    <>\n      <Navigation user={user} />\n      <main className=\"flex-1 flex flex-col\">\n        {children}\n      </main>\n      <footer className=\"bg-white/60 backdrop-blur-sm border-t border-amber-200 py-4\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center text-sm text-gray-600\">\n            <p>木魚功德 - Traditional Merit Accumulation Game</p>\n            <p className=\"mt-1 text-xs\">\n              Click mindfully • Complete missions • Build merit\n            </p>\n          </div>\n        </div>\n      </footer>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWe,SAAS,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAmB;IACpE,qBACE;;0BACE,6LAAC,mIAAA,CAAA,UAAU;gBAAC,MAAM;;;;;;0BAClB,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;;;AAQxC;KAnBwB", "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/components/AuthForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { signUp, signIn } from '@/lib/supabase-utils'\nimport { HK_DISTRICTS, type HKDistrict } from '@/lib/supabase'\n\nexport default function AuthForm() {\n  const [isSignUp, setIsSignUp] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [success, setSuccess] = useState<string | null>(null)\n  \n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    username: '',\n    clan: 'Central and Western' as HKDistrict\n  })\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError(null)\n    setSuccess(null)\n\n    try {\n      if (isSignUp) {\n        const { error } = await signUp(\n          formData.email,\n          formData.password,\n          formData.username,\n          formData.clan\n        )\n        \n        if (error) {\n          setError(error.message)\n        } else {\n          setSuccess('Account created! Please check your email to verify your account.')\n        }\n      } else {\n        const { error } = await signIn(formData.email, formData.password)\n        \n        if (error) {\n          setError(error.message)\n        }\n      }\n    } catch (err) {\n      setError('An unexpected error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <div className=\"text-6xl mb-4\">🪵🐟</div>\n          <h1 className=\"text-3xl font-bold text-amber-800 mb-2\">\n            木魚功德\n          </h1>\n          <p className=\"text-amber-600\">\n            Traditional Merit Accumulation Game\n          </p>\n        </div>\n\n        {/* Auth Form */}\n        <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-amber-200\">\n          <div className=\"mb-6\">\n            <div className=\"flex rounded-lg bg-amber-100 p-1\">\n              <button\n                type=\"button\"\n                onClick={() => setIsSignUp(false)}\n                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n                  !isSignUp\n                    ? 'bg-white text-amber-800 shadow-sm'\n                    : 'text-amber-600 hover:text-amber-800'\n                }`}\n              >\n                Sign In\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => setIsSignUp(true)}\n                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n                  isSignUp\n                    ? 'bg-white text-amber-800 shadow-sm'\n                    : 'text-amber-600 hover:text-amber-800'\n                }`}\n              >\n                Sign Up\n              </button>\n            </div>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {/* Email */}\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-amber-800 mb-1\">\n                Email\n              </label>\n              <input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n\n            {/* Password */}\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-amber-800 mb-1\">\n                Password\n              </label>\n              <input\n                type=\"password\"\n                id=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleInputChange}\n                required\n                minLength={6}\n                className=\"w-full px-3 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                placeholder=\"Enter your password\"\n              />\n            </div>\n\n            {/* Sign Up Only Fields */}\n            {isSignUp && (\n              <>\n                {/* Username */}\n                <div>\n                  <label htmlFor=\"username\" className=\"block text-sm font-medium text-amber-800 mb-1\">\n                    Username\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"username\"\n                    name=\"username\"\n                    value={formData.username}\n                    onChange={handleInputChange}\n                    required\n                    minLength={3}\n                    maxLength={20}\n                    className=\"w-full px-3 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                    placeholder=\"Choose a username\"\n                  />\n                </div>\n\n                {/* Clan Selection */}\n                <div>\n                  <label htmlFor=\"clan\" className=\"block text-sm font-medium text-amber-800 mb-1\">\n                    Choose Your District (Clan)\n                  </label>\n                  <select\n                    id=\"clan\"\n                    name=\"clan\"\n                    value={formData.clan}\n                    onChange={handleInputChange}\n                    required\n                    className=\"w-full px-3 py-2 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                  >\n                    {HK_DISTRICTS.map((district) => (\n                      <option key={district} value={district}>\n                        {district}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </>\n            )}\n\n            {/* Error Message */}\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n                <p className=\"text-sm text-red-600\">{error}</p>\n              </div>\n            )}\n\n            {/* Success Message */}\n            {success && (\n              <div className=\"bg-green-50 border border-green-200 rounded-lg p-3\">\n                <p className=\"text-sm text-green-600\">{success}</p>\n              </div>\n            )}\n\n            {/* Submit Button */}\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"w-full bg-amber-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            >\n              {loading ? 'Loading...' : isSignUp ? 'Create Account' : 'Sign In'}\n            </button>\n          </form>\n\n          {/* Additional Info */}\n          <div className=\"mt-6 text-center text-sm text-amber-600\">\n            <p>\n              Join your Hong Kong district clan and compete for merit!\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,UAAU;QACV,MAAM;IACR;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,IAAI,UAAU;gBACZ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,kIAAA,CAAA,SAAM,AAAD,EAC3B,SAAS,KAAK,EACd,SAAS,QAAQ,EACjB,SAAS,QAAQ,EACjB,SAAS,IAAI;gBAGf,IAAI,OAAO;oBACT,SAAS,MAAM,OAAO;gBACxB,OAAO;oBACL,WAAW;gBACb;YACF,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,kIAAA,CAAA,SAAM,AAAD,EAAE,SAAS,KAAK,EAAE,SAAS,QAAQ;gBAEhE,IAAI,OAAO;oBACT,SAAS,MAAM,OAAO;gBACxB;YACF;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAE,WAAU;sCAAiB;;;;;;;;;;;;8BAMhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAW,CAAC,kEAAkE,EAC5E,CAAC,WACG,sCACA,uCACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAW,CAAC,kEAAkE,EAC5E,WACI,sCACA,uCACJ;kDACH;;;;;;;;;;;;;;;;;sCAML,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAgD;;;;;;sDAGjF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAKhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAgD;;;;;;sDAGpF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,QAAQ;4CACR,WAAW;4CACX,WAAU;4CACV,aAAY;;;;;;;;;;;;gCAKf,0BACC;;sDAEE,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAAgD;;;;;;8DAGpF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,QAAQ;oDACR,WAAW;oDACX,WAAW;oDACX,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAKhB,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAAgD;;;;;;8DAGhF,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,QAAQ;oDACR,WAAU;8DAET,yHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,yBACjB,6LAAC;4DAAsB,OAAO;sEAC3B;2DADU;;;;;;;;;;;;;;;;;;gCAUtB,uBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;gCAKxC,yBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;8CAK3C,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,eAAe,WAAW,mBAAmB;;;;;;;;;;;;sCAK5D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQf;GApNwB;KAAA", "debugId": null}}, {"offset": {"line": 978, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/components/SetupNotice.tsx"], "sourcesContent": ["export default function SetupNotice() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center p-4\">\n      <div className=\"max-w-2xl w-full\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <div className=\"text-6xl mb-4\">🪵🐟</div>\n          <h1 className=\"text-3xl font-bold text-amber-800 mb-2\">\n            木魚功德\n          </h1>\n          <p className=\"text-amber-600\">\n            Traditional Merit Accumulation Game\n          </p>\n        </div>\n\n        {/* Setup Instructions */}\n        <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-amber-200\">\n          <h2 className=\"text-2xl font-bold text-amber-800 mb-6\">\n            🚀 Setup Required\n          </h2>\n          \n          <div className=\"space-y-6\">\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <h3 className=\"font-semibold text-blue-800 mb-2\">\n                1. Create a Supabase Project\n              </h3>\n              <p className=\"text-blue-700 text-sm\">\n                Go to <a href=\"https://supabase.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline\">supabase.com</a> and create a new project.\n              </p>\n            </div>\n\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n              <h3 className=\"font-semibold text-green-800 mb-2\">\n                2. Set up Database Schema\n              </h3>\n              <p className=\"text-green-700 text-sm mb-2\">\n                Run the SQL commands from <code className=\"bg-green-100 px-1 rounded\">supabase-schema.sql</code> in your Supabase SQL editor.\n              </p>\n            </div>\n\n            <div className=\"bg-purple-50 border border-purple-200 rounded-lg p-4\">\n              <h3 className=\"font-semibold text-purple-800 mb-2\">\n                3. Configure Environment Variables\n              </h3>\n              <p className=\"text-purple-700 text-sm mb-3\">\n                Update your <code className=\"bg-purple-100 px-1 rounded\">.env.local</code> file with your Supabase credentials:\n              </p>\n              <div className=\"bg-gray-900 text-green-400 p-3 rounded text-xs font-mono\">\n                <div>NEXT_PUBLIC_SUPABASE_URL=your_project_url</div>\n                <div>NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key</div>\n                <div>SUPABASE_SERVICE_ROLE_KEY=your_service_role_key</div>\n              </div>\n            </div>\n\n            <div className=\"bg-amber-50 border border-amber-200 rounded-lg p-4\">\n              <h3 className=\"font-semibold text-amber-800 mb-2\">\n                4. Restart Development Server\n              </h3>\n              <p className=\"text-amber-700 text-sm\">\n                After updating the environment variables, restart your development server with <code className=\"bg-amber-100 px-1 rounded\">npm run dev</code>.\n              </p>\n            </div>\n          </div>\n\n          <div className=\"mt-8 p-4 bg-gray-50 rounded-lg\">\n            <h4 className=\"font-semibold text-gray-800 mb-2\">\n              📋 Features Overview\n            </h4>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• Interactive 木魚 (wooden fish) clicking with animations</li>\n              <li>• Time-based mission system (3 missions per hour, 08:00-22:59)</li>\n              <li>• Anti-cheat protection with server-side validation</li>\n              <li>• Hong Kong district-based clan system</li>\n              <li>• Real-time leaderboards and scoring</li>\n              <li>• User authentication and profile management</li>\n              <li>• Login streak tracking and milestones</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAE,WAAU;sCAAiB;;;;;;;;;;;;8BAMhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAIvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,6LAAC;4CAAE,WAAU;;gDAAwB;8DAC7B,6LAAC;oDAAE,MAAK;oDAAuB,QAAO;oDAAS,KAAI;oDAAsB,WAAU;8DAAY;;;;;;gDAAgB;;;;;;;;;;;;;8CAIzH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAGlD,6LAAC;4CAAE,WAAU;;gDAA8B;8DACf,6LAAC;oDAAK,WAAU;8DAA4B;;;;;;gDAA0B;;;;;;;;;;;;;8CAIpG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,6LAAC;4CAAE,WAAU;;gDAA+B;8DAC9B,6LAAC;oDAAK,WAAU;8DAA6B;;;;;;gDAAiB;;;;;;;sDAE5E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;;;;;;;;;;;;;8CAIT,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAGlD,6LAAC;4CAAE,WAAU;;gDAAyB;8DAC2C,6LAAC;oDAAK,WAAU;8DAA4B;;;;;;gDAAkB;;;;;;;;;;;;;;;;;;;sCAKnJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAGjD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;KAlFwB", "debugId": null}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\nimport GameLayout from '@/components/GameLayout'\nimport AuthForm from '@/components/AuthForm'\nimport SetupNotice from '@/components/SetupNotice'\nimport WoodenFish from '@/components/WoodenFish'\n\nexport default function Home() {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [clickCount, setClickCount] = useState(0)\n\n  useEffect(() => {\n    if (!supabase) {\n      setLoading(false)\n      return\n    }\n\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user ?? null)\n      setLoading(false)\n    })\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      setUser(session?.user ?? null)\n      setLoading(false)\n    })\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const handleFishClick = (clickData: { x: number; y: number; timestamp: number }) => {\n    console.log('Fish clicked:', clickData)\n    setClickCount(prev => prev + 1)\n\n    // TODO: Send click to server for mission validation\n    // This will be implemented when we add the mission system\n  }\n\n  // Show setup notice if Supabase is not configured\n  if (!supabase) {\n    return <SetupNotice />\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">🪵🐟</div>\n          <div className=\"text-lg text-amber-800 font-medium\">Loading...</div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return <AuthForm />\n  }\n\n  return (\n    <GameLayout user={user}>\n      <div className=\"flex-1 flex flex-col items-center justify-center p-4\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-amber-800 mb-2\">\n            木魚功德\n          </h1>\n          <p className=\"text-lg text-amber-600\">\n            Wooden Fish Merit Game\n          </p>\n        </div>\n\n        {/* Placeholder for the wooden fish component */}\n        <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-amber-200\">\n          <div className=\"text-center\">\n            <div className=\"text-8xl mb-4 cursor-pointer hover:scale-105 transition-transform\">\n              🪵🐟\n            </div>\n            <p className=\"text-amber-700 font-medium\">\n              Click the wooden fish to gain merit!\n            </p>\n            <p className=\"text-sm text-amber-600 mt-2\">\n              (Wooden fish component will be implemented next)\n            </p>\n          </div>\n        </div>\n\n        {/* Placeholder for missions and leaderboard */}\n        <div className=\"mt-8 grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-4xl\">\n          <div className=\"bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-amber-200\">\n            <h3 className=\"text-lg font-semibold text-amber-800 mb-4\">\n              Current Missions\n            </h3>\n            <p className=\"text-amber-600\">\n              Mission system will be implemented next\n            </p>\n          </div>\n\n          <div className=\"bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-amber-200\">\n            <h3 className=\"text-lg font-semibold text-amber-800 mb-4\">\n              Leaderboard\n            </h3>\n            <p className=\"text-amber-600\">\n              Leaderboard will be implemented next\n            </p>\n          </div>\n        </div>\n      </div>\n    </GameLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;;;AAPA;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;gBACb,WAAW;gBACX;YACF;YAEA,sBAAsB;YACtB,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI;kCAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;oBACpD,QAAQ,SAAS,QAAQ;oBACzB,WAAW;gBACb;;YAEA,0BAA0B;YAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;kCAAC,CAAC,QAAQ;oBAC3C,QAAQ,SAAS,QAAQ;oBACzB,WAAW;gBACb;;YAEA;kCAAO,IAAM,aAAa,WAAW;;QACvC;yBAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,QAAQ,GAAG,CAAC,iBAAiB;QAC7B,cAAc,CAAA,OAAQ,OAAO;IAE7B,oDAAoD;IACpD,0DAA0D;IAC5D;IAEA,kDAAkD;IAClD,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;QACb,qBAAO,6LAAC,oIAAA,CAAA,UAAW;;;;;IACrB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6LAAC;wBAAI,WAAU;kCAAqC;;;;;;;;;;;;;;;;;IAI5D;IAEA,IAAI,CAAC,MAAM;QACT,qBAAO,6LAAC,iIAAA,CAAA,UAAQ;;;;;IAClB;IAEA,qBACE,6LAAC,mIAAA,CAAA,UAAU;QAAC,MAAM;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;8BAMxC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAoE;;;;;;0CAGnF,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCAAE,WAAU;0CAA8B;;;;;;;;;;;;;;;;;8BAO/C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAAiB;;;;;;;;;;;;sCAKhC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C;GA1GwB;KAAA", "debugId": null}}]}