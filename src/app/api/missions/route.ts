import { NextRequest, NextResponse } from 'next/server'
import { getUserFromRequest } from '@/lib/auth'
import { getUserMissions, createMission, getDatabase } from '@/lib/database'
import {
  getMissionsForCurrentHour,
  isWithinActiveHours,
  getCurrentHourSlot,
  getNextMissionTime
} from '@/lib/missionUtils'

// GET /api/missions - Get current missions for user
export async function GET(request: NextRequest) {
  try {
    // Get user from auth
    const user = await getUserFromRequest(request)

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = user.id

    // Check if we're within active hours
    if (!isWithinActiveHours()) {
      const nextMissionTime = getNextMissionTime()
      return NextResponse.json({
        missions: [],
        activeHours: false,
        nextMissionTime: nextMissionTime.toISOString(),
        message: 'Missions are only available between 08:00-22:59 HK time'
      })
    }

    const currentHourSlot = getCurrentHourSlot()

    // Get existing missions for current hour
    const db = getDatabase()
    const currentHourStart = new Date(currentHourSlot).toISOString()
    const currentHourEnd = new Date(new Date(currentHourSlot).getTime() + 60 * 60 * 1000).toISOString()

    const existingMissions = db.prepare(`
      SELECT * FROM missions
      WHERE user_id = ?
      AND created_at >= ?
      AND created_at < ?
      ORDER BY created_at ASC
    `).all(userId, currentHourStart, currentHourEnd)

    // If we don't have 3 missions for this hour, generate them
    if (existingMissions.length < 3) {
      const missionsToGenerate = getMissionsForCurrentHour(userId)
      const missionsToInsert = missionsToGenerate.slice(existingMissions.length)

      const newMissions = []
      for (const missionData of missionsToInsert) {
        const mission = createMission({
          user_id: missionData.user_id,
          target_clicks: missionData.target_clicks,
          expires_at: missionData.expires_at
        })
        newMissions.push(mission)
      }

      // Combine existing and new missions
      const allMissions = [...existingMissions, ...newMissions]

      return NextResponse.json({
        missions: allMissions,
        activeHours: true,
        currentHourSlot,
        generated: missionsToInsert.length
      })
    }

    return NextResponse.json({
      missions: existingMissions,
      activeHours: true,
      currentHourSlot
    })

  } catch (error) {
    console.error('Mission API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/missions - Create new missions (admin/debug only)
export async function POST(request: NextRequest) {
  try {
    // Get user from auth
    const user = await getUserFromRequest(request)

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = user.id
    const body = await request.json()

    // Force generate missions (for testing)
    if (body.force === true) {
      const missionsToGenerate = getMissionsForCurrentHour(userId)

      const newMissions = []
      for (const missionData of missionsToGenerate) {
        const mission = createMission({
          user_id: missionData.user_id,
          target_clicks: missionData.target_clicks,
          expires_at: missionData.expires_at
        })
        newMissions.push(mission)
      }

      return NextResponse.json({
        missions: newMissions,
        message: 'Missions force generated'
      })
    }

    return NextResponse.json({ error: 'Invalid request' }, { status: 400 })

  } catch (error) {
    console.error('Mission POST API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
