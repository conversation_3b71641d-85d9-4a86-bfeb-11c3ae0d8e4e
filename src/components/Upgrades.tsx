'use client'

import { useState, useEffect } from 'react'
import { ShoppingCart, Lock, TrendingUp, Zap } from 'lucide-react'
import { UPGRADES, getUpgradeCost, isUpgradeUnlocked, formatNumber, type Upgrade } from '@/lib/upgrades'

interface UserUpgrade {
  upgrade_id: string
  level: number
  total_spent: number
}

interface UpgradesProps {
  userMerit: number
  onUpgradePurchase: (upgradeId: string, cost: number) => Promise<boolean>
}

export default function Upgrades({ userMerit, onUpgradePurchase }: UpgradesProps) {
  const [userUpgrades, setUserUpgrades] = useState<UserUpgrade[]>([])
  const [loading, setLoading] = useState(true)
  const [purchasing, setPurchasing] = useState<string | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>('tools')

  const categories = [
    { id: 'tools', name: 'Tools', icon: '🔨', description: 'Increase clicking power' },
    { id: 'incense', name: 'Incense', icon: '🕯️', description: 'Passive merit generation' },
    { id: 'meditation', name: 'Meditation', icon: '🧘', description: 'Mission bonuses' },
    { id: 'temple', name: 'Temple', icon: '⛩️', description: 'Major upgrades' },
    { id: 'spiritual', name: 'Spiritual', icon: '📜', description: 'Special effects' }
  ]

  useEffect(() => {
    fetchUserUpgrades()
  }, [])

  const fetchUserUpgrades = async () => {
    try {
      const response = await fetch('/api/upgrades')
      if (response.ok) {
        const data = await response.json()
        setUserUpgrades(data.upgrades || [])
      }
    } catch (error) {
      console.error('Error fetching upgrades:', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePurchase = async (upgrade: Upgrade) => {
    const userUpgrade = userUpgrades.find(u => u.upgrade_id === upgrade.id)
    const currentLevel = userUpgrade?.level || 0
    const cost = getUpgradeCost(upgrade, currentLevel)

    if (userMerit < cost) return

    setPurchasing(upgrade.id)
    
    try {
      const success = await onUpgradePurchase(upgrade.id, cost)
      if (success) {
        // Update local state
        setUserUpgrades(prev => {
          const existing = prev.find(u => u.upgrade_id === upgrade.id)
          if (existing) {
            return prev.map(u => 
              u.upgrade_id === upgrade.id 
                ? { ...u, level: u.level + 1, total_spent: u.total_spent + cost }
                : u
            )
          } else {
            return [...prev, { upgrade_id: upgrade.id, level: 1, total_spent: cost }]
          }
        })
      }
    } catch (error) {
      console.error('Error purchasing upgrade:', error)
    } finally {
      setPurchasing(null)
    }
  }

  const getUserUpgradeLevel = (upgradeId: string): number => {
    return userUpgrades.find(u => u.upgrade_id === upgradeId)?.level || 0
  }

  const getTotalUpgradeCount = (): number => {
    return userUpgrades.reduce((total, upgrade) => total + upgrade.level, 0)
  }

  const filteredUpgrades = UPGRADES.filter(upgrade => upgrade.category === selectedCategory)

  if (loading) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
        <div className="animate-pulse">
          <div className="h-6 bg-amber-200 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-amber-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-800 flex items-center">
          <ShoppingCart className="w-6 h-6 text-amber-600 mr-2" />
          Upgrades
        </h2>
        <div className="text-sm text-amber-700">
          Merit: <span className="font-bold">{formatNumber(userMerit)}</span>
        </div>
      </div>

      {/* Category Tabs */}
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1 ${
              selectedCategory === category.id
                ? 'bg-amber-100 text-amber-800 border border-amber-300'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            <span>{category.icon}</span>
            <span>{category.name}</span>
          </button>
        ))}
      </div>

      {/* Current Category Description */}
      <div className="mb-4 p-3 bg-amber-50 rounded-lg border border-amber-200">
        <p className="text-sm text-amber-700">
          {categories.find(c => c.id === selectedCategory)?.description}
        </p>
      </div>

      {/* Upgrades Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {filteredUpgrades.map((upgrade) => {
          const userLevel = getUserUpgradeLevel(upgrade.id)
          const cost = getUpgradeCost(upgrade, userLevel)
          const canAfford = userMerit >= cost
          const isUnlocked = isUpgradeUnlocked(upgrade, userMerit, getTotalUpgradeCount())
          const isPurchasing = purchasing === upgrade.id

          return (
            <div
              key={upgrade.id}
              className={`p-4 rounded-lg border-2 transition-all ${
                !isUnlocked
                  ? 'bg-gray-100 border-gray-300 opacity-50'
                  : canAfford
                  ? 'bg-white border-amber-300 hover:border-amber-400 hover:shadow-md'
                  : 'bg-gray-50 border-gray-300'
              }`}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl">{upgrade.icon}</span>
                  <div>
                    <h3 className="font-medium text-gray-800">{upgrade.name}</h3>
                    {userLevel > 0 && (
                      <div className="text-xs text-amber-600 font-medium">
                        Level {userLevel}
                      </div>
                    )}
                  </div>
                </div>
                {!isUnlocked && <Lock className="w-4 h-4 text-gray-400" />}
              </div>

              <p className="text-sm text-gray-600 mb-3">{upgrade.description}</p>

              {/* Effect Display */}
              <div className="flex items-center space-x-1 mb-3 text-xs">
                {upgrade.effect.type === 'click_power' && <Zap className="w-3 h-3 text-blue-500" />}
                {upgrade.effect.type === 'passive_generation' && <TrendingUp className="w-3 h-3 text-green-500" />}
                <span className="text-gray-600">
                  {upgrade.effect.type === 'click_power' && `+${upgrade.effect.value} click power`}
                  {upgrade.effect.type === 'passive_generation' && `+${upgrade.effect.value}/sec passive`}
                  {upgrade.effect.type === 'mission_bonus' && `+${(upgrade.effect.value * 100).toFixed(0)}% mission bonus`}
                  {upgrade.effect.type === 'special' && `${upgrade.effect.value}x multiplier`}
                </span>
              </div>

              {/* Purchase Button */}
              <button
                onClick={() => handlePurchase(upgrade)}
                disabled={!isUnlocked || !canAfford || isPurchasing}
                className={`w-full py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                  !isUnlocked
                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    : canAfford && !isPurchasing
                    ? 'bg-amber-500 text-white hover:bg-amber-600'
                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                }`}
              >
                {!isUnlocked ? (
                  `Unlock at ${formatNumber(upgrade.unlockRequirement?.value as number || 0)} merit`
                ) : isPurchasing ? (
                  'Purchasing...'
                ) : (
                  `Buy for ${formatNumber(cost)} merit`
                )}
              </button>
            </div>
          )
        })}
      </div>

      {filteredUpgrades.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <ShoppingCart className="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p>No upgrades available in this category</p>
        </div>
      )}
    </div>
  )
}
