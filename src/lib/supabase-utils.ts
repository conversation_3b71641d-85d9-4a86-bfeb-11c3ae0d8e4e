import { supabase } from './supabase'
import type { User, Mission, HKDistrict } from './supabase'

// Auth utilities
export const signUp = async (email: string, password: string, username: string, clan: HKDistrict) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        username,
        clan
      }
    }
  })
  return { data, error }
}

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  return { data, error }
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  return { error }
}

export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  return { user, error }
}

// User utilities
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()
  
  return { data, error }
}

export const updateUserProfile = async (userId: string, updates: Partial<User>) => {
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()
  
  return { data, error }
}

export const updateLoginStreak = async (userId: string) => {
  const { data, error } = await supabase
    .rpc('update_login_streak', { user_uuid: userId })
  
  return { data, error }
}

// Mission utilities
export const getUserActiveMissions = async (userId: string) => {
  const { data, error } = await supabase
    .from('missions')
    .select('*')
    .eq('user_id', userId)
    .eq('status', 'active')
    .gt('expires_at', new Date().toISOString())
    .order('created_at', { ascending: false })
  
  return { data, error }
}

export const createMission = async (userId: string, targetClicks: number, expiresAt: string) => {
  const { data, error } = await supabase
    .from('missions')
    .insert({
      user_id: userId,
      target_clicks: targetClicks,
      expires_at: expiresAt
    })
    .select()
    .single()
  
  return { data, error }
}

export const updateMissionProgress = async (missionId: string, completedClicks: number) => {
  const { data, error } = await supabase
    .from('missions')
    .update({ 
      completed_clicks: completedClicks,
      updated_at: new Date().toISOString()
    })
    .eq('id', missionId)
    .select()
    .single()
  
  return { data, error }
}

export const completeMission = async (missionId: string, scoreAwarded: number) => {
  const { data, error } = await supabase
    .from('missions')
    .update({ 
      status: 'completed',
      score_awarded: scoreAwarded,
      updated_at: new Date().toISOString()
    })
    .eq('id', missionId)
    .select()
    .single()
  
  return { data, error }
}

export const failMission = async (missionId: string, penaltyScore: number) => {
  const { data, error } = await supabase
    .from('missions')
    .update({ 
      status: 'failed',
      score_awarded: penaltyScore,
      updated_at: new Date().toISOString()
    })
    .eq('id', missionId)
    .select()
    .single()
  
  return { data, error }
}

// Click logging for anti-cheat
export const logClick = async (userId: string, missionId: string, ipAddress?: string, userAgent?: string) => {
  const { data, error } = await supabase
    .from('click_logs')
    .insert({
      user_id: userId,
      mission_id: missionId,
      ip_address: ipAddress,
      user_agent: userAgent
    })
  
  return { data, error }
}

// Leaderboard utilities
export const getGlobalLeaderboard = async (limit: number = 10) => {
  const { data, error } = await supabase
    .from('leaderboard')
    .select('*')
    .limit(limit)
  
  return { data, error }
}

export const getClanLeaderboard = async (clan: string, limit: number = 10) => {
  const { data, error } = await supabase
    .from('leaderboard')
    .select('*')
    .eq('clan', clan)
    .limit(limit)
  
  return { data, error }
}

export const getUserRank = async (userId: string) => {
  const { data, error } = await supabase
    .from('leaderboard')
    .select('global_rank, clan_rank')
    .eq('id', userId)
    .single()
  
  return { data, error }
}

// Mission generation utilities
export const shouldGenerateMissions = () => {
  const now = new Date()
  const hour = now.getHours()
  
  // No missions between 23:00 - 07:59
  return hour >= 8 && hour <= 22
}

export const generateMissionTargets = (): number[] => {
  // Generate 3 random targets between 10-60 clicks
  const targets = []
  for (let i = 0; i < 3; i++) {
    targets.push(Math.floor(Math.random() * 51) + 10) // 10-60 clicks
  }
  return targets.sort((a, b) => a - b) // Sort ascending
}

export const getMissionExpiryTime = (): string => {
  const now = new Date()
  const expiry = new Date(now.getTime() + 60 * 60 * 1000) // 1 hour from now
  return expiry.toISOString()
}
