'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Trophy, Target, Clock, Zap, TrendingUp, Award } from 'lucide-react'

interface UserStatsProps {
  totalScore: number
  clickPower: number
  passiveGeneration: number
}

interface UserStatistics {
  totalClicks: number
  missionsCompleted: number
  loginStreak: number
  totalPlayTime: number
  averageClicksPerMinute: number
  highestMissionScore: number
}

export default function UserStats({ totalScore, clickPower, passiveGeneration }: UserStatsProps) {
  const [stats, setStats] = useState<UserStatistics>({
    totalClicks: 0,
    missionsCompleted: 0,
    loginStreak: 0,
    totalPlayTime: 0,
    averageClicksPerMinute: 0,
    highestMissionScore: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchUserStats()
  }, [])

  const fetchUserStats = async () => {
    try {
      const response = await fetch('/api/user-stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Error fetching user stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
    return num.toString()
  }

  const formatTime = (minutes: number): string => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `${hours}h ${remainingMinutes}m`
  }

  if (loading) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
        <div className="animate-pulse">
          <div className="h-6 bg-amber-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-4 bg-amber-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const statItems = [
    {
      icon: Trophy,
      label: 'Total Merit',
      value: formatNumber(totalScore),
      color: 'text-amber-600'
    },
    {
      icon: Target,
      label: 'Missions Completed',
      value: stats.missionsCompleted.toString(),
      color: 'text-green-600'
    },
    {
      icon: Zap,
      label: 'Click Power',
      value: clickPower.toString(),
      color: 'text-blue-600'
    },
    {
      icon: TrendingUp,
      label: 'Passive Generation',
      value: passiveGeneration > 0 ? `${passiveGeneration.toFixed(1)}/sec` : 'None',
      color: 'text-purple-600'
    },
    {
      icon: Clock,
      label: 'Login Streak',
      value: `${stats.loginStreak} days`,
      color: 'text-orange-600'
    },
    {
      icon: Award,
      label: 'Total Clicks',
      value: formatNumber(stats.totalClicks),
      color: 'text-red-600'
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200"
    >
      <h3 className="text-lg font-bold text-amber-800 mb-4 flex items-center">
        <Trophy className="w-5 h-5 mr-2" />
        Your Statistics
      </h3>

      <div className="space-y-4">
        {statItems.map((item, index) => (
          <motion.div
            key={item.label}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-center justify-between p-3 bg-white/50 rounded-lg border border-amber-100"
          >
            <div className="flex items-center">
              <item.icon className={`w-4 h-4 mr-3 ${item.color}`} />
              <span className="text-sm font-medium text-gray-700">{item.label}</span>
            </div>
            <span className={`text-sm font-bold ${item.color}`}>
              {item.value}
            </span>
          </motion.div>
        ))}
      </div>

      {/* Quick Tips */}
      <div className="mt-6 p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-200">
        <h4 className="text-sm font-semibold text-amber-800 mb-2">💡 Quick Tips</h4>
        <ul className="text-xs text-amber-700 space-y-1">
          <li>• Complete missions for bonus merit</li>
          <li>• Buy upgrades to increase click power</li>
          <li>• Passive generation works even offline</li>
          <li>• Login daily to maintain your streak</li>
        </ul>
      </div>
    </motion.div>
  )
}
