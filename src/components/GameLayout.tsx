'use client'

import { ReactNode } from 'react'
import Navigation from './Navigation'
import { User } from '@/lib/types'

interface GameLayoutProps {
  children: ReactNode
  user: User | null
}

export default function GameLayout({ children, user }: GameLayoutProps) {
  return (
    <>
      <Navigation user={user} />
      <main className="flex-1 flex flex-col">
        {children}
      </main>
      <footer className="bg-white/60 backdrop-blur-sm border-t border-amber-200 py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-sm text-gray-600">
            <p>木魚功德 - Traditional Merit Accumulation Game</p>
            <p className="mt-1 text-xs">
              Click mindfully • Complete missions • Build merit
            </p>
          </div>
        </div>
      </footer>
    </>
  )
}
