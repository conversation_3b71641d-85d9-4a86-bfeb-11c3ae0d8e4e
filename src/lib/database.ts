import Database from 'better-sqlite3';
import { join } from 'path';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// Import types from types file
import type { User, ClickLog, UserStats, HKDistrict } from './types';
import { HK_DISTRICTS } from './types';
import type { Mission } from './missionUtils';

// Re-export types for external use
export type { User, Mission, ClickLog, UserStats, HKDistrict };
export { HK_DISTRICTS };

// Database instance
let db: Database.Database | null = null;

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

export function getDatabase(): Database.Database {
  if (!db) {
    const dbPath = join(process.cwd(), 'wooden-fish.db');
    db = new Database(dbPath);
    
    // Enable foreign keys
    db.pragma('foreign_keys = ON');
    
    // Initialize database schema
    initializeDatabase(db);
  }
  return db;
}

function initializeDatabase(database: Database.Database) {
  // Create users table
  database.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      display_name TEXT,
      clan_district TEXT NOT NULL CHECK (clan_district IN (${HK_DISTRICTS.map(d => `'${d}'`).join(', ')})),
      total_score INTEGER DEFAULT 0,
      missions_completed INTEGER DEFAULT 0,
      login_streak INTEGER DEFAULT 0,
      last_login_date TEXT,
      created_at TEXT DEFAULT (datetime('now')),
      updated_at TEXT DEFAULT (datetime('now'))
    )
  `);

  // Create user_stats table
  database.exec(`
    CREATE TABLE IF NOT EXISTS user_stats (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      date TEXT DEFAULT (date('now')),
      daily_score INTEGER DEFAULT 0,
      daily_missions_completed INTEGER DEFAULT 0,
      daily_clicks INTEGER DEFAULT 0,
      created_at TEXT DEFAULT (datetime('now')),
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
      UNIQUE(user_id, date)
    )
  `);

  // Create missions table
  database.exec(`
    CREATE TABLE IF NOT EXISTS missions (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      mission_type TEXT DEFAULT 'hourly',
      target_clicks INTEGER NOT NULL,
      completed_clicks INTEGER DEFAULT 0,
      status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'failed')),
      score_awarded INTEGER DEFAULT 0,
      created_at TEXT DEFAULT (datetime('now')),
      expires_at TEXT NOT NULL,
      completed_at TEXT,
      updated_at TEXT DEFAULT (datetime('now')),
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )
  `);

  // Create click_logs table
  database.exec(`
    CREATE TABLE IF NOT EXISTS click_logs (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      mission_id TEXT NOT NULL,
      click_x REAL,
      click_y REAL,
      click_timestamp TEXT,
      server_timestamp TEXT DEFAULT (datetime('now')),
      created_at TEXT DEFAULT (datetime('now')),
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
      FOREIGN KEY (mission_id) REFERENCES missions (id) ON DELETE CASCADE
    )
  `);

  // Create indexes for better performance
  database.exec(`
    CREATE INDEX IF NOT EXISTS idx_missions_user_id ON missions (user_id);
    CREATE INDEX IF NOT EXISTS idx_missions_status ON missions (status);
    CREATE INDEX IF NOT EXISTS idx_click_logs_user_id ON click_logs (user_id);
    CREATE INDEX IF NOT EXISTS idx_click_logs_mission_id ON click_logs (mission_id);
    CREATE INDEX IF NOT EXISTS idx_user_stats_user_date ON user_stats (user_id, date);
  `);

  // Create trigger to update updated_at timestamp
  database.exec(`
    CREATE TRIGGER IF NOT EXISTS update_users_updated_at
    AFTER UPDATE ON users
    BEGIN
      UPDATE users SET updated_at = datetime('now') WHERE id = NEW.id;
    END;
  `);

  database.exec(`
    CREATE TRIGGER IF NOT EXISTS update_missions_updated_at
    AFTER UPDATE ON missions
    BEGIN
      UPDATE missions SET updated_at = datetime('now') WHERE id = NEW.id;
    END;
  `);

  // Create user_upgrades table for Cookie Clicker-style upgrades
  database.exec(`
    CREATE TABLE IF NOT EXISTS user_upgrades (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      upgrade_id TEXT NOT NULL,
      level INTEGER DEFAULT 0,
      total_spent INTEGER DEFAULT 0,
      created_at TEXT DEFAULT (datetime('now')),
      updated_at TEXT DEFAULT (datetime('now')),
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
      UNIQUE(user_id, upgrade_id)
    )
  `);

  // Create passive_generation table to track offline earnings
  database.exec(`
    CREATE TABLE IF NOT EXISTS passive_generation (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      last_calculated TEXT DEFAULT (datetime('now')),
      generation_rate REAL DEFAULT 0,
      total_generated INTEGER DEFAULT 0,
      created_at TEXT DEFAULT (datetime('now')),
      updated_at TEXT DEFAULT (datetime('now')),
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
      UNIQUE(user_id)
    )
  `);

  // Add upgrade-related fields to users table (only if they don't exist)
  try {
    database.exec(`ALTER TABLE users ADD COLUMN click_power INTEGER DEFAULT 1;`);
  } catch (error) {
    // Column already exists, ignore
  }

  try {
    database.exec(`ALTER TABLE users ADD COLUMN passive_generation REAL DEFAULT 0;`);
  } catch (error) {
    // Column already exists, ignore
  }

  try {
    database.exec(`ALTER TABLE users ADD COLUMN mission_bonus REAL DEFAULT 0;`);
  } catch (error) {
    // Column already exists, ignore
  }

  try {
    database.exec(`ALTER TABLE users ADD COLUMN last_passive_update TEXT;`);
    // Set default value for existing users
    database.exec(`UPDATE users SET last_passive_update = datetime('now') WHERE last_passive_update IS NULL;`);
  } catch (error) {
    // Column already exists, ignore
  }

  // Add completed_at column to missions table if it doesn't exist
  try {
    database.exec(`ALTER TABLE missions ADD COLUMN completed_at TEXT;`);
  } catch (error) {
    // Column already exists, ignore
  }

  // Create triggers for user_upgrades
  database.exec(`
    CREATE TRIGGER IF NOT EXISTS update_user_upgrades_updated_at
    AFTER UPDATE ON user_upgrades
    BEGIN
      UPDATE user_upgrades SET updated_at = datetime('now') WHERE id = NEW.id;
    END;
  `);

  database.exec(`
    CREATE TRIGGER IF NOT EXISTS update_passive_generation_updated_at
    AFTER UPDATE ON passive_generation
    BEGIN
      UPDATE passive_generation SET updated_at = datetime('now') WHERE id = NEW.id;
    END;
  `);
}

// Authentication functions
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

export function generateToken(userId: string): string {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' });
}

export function verifyToken(token: string): { userId: string } | null {
  try {
    return jwt.verify(token, JWT_SECRET) as { userId: string };
  } catch {
    return null;
  }
}

// User functions
export function createUser(userData: {
  username: string;
  email: string;
  password_hash: string;
  display_name: string;
  clan_district: HKDistrict;
}): User {
  const db = getDatabase();
  const id = crypto.randomUUID();
  
  const stmt = db.prepare(`
    INSERT INTO users (id, username, email, password_hash, display_name, clan_district)
    VALUES (?, ?, ?, ?, ?, ?)
  `);
  
  stmt.run(id, userData.username, userData.email, userData.password_hash, userData.display_name, userData.clan_district);
  
  return getUserById(id)!;
}

export function getUserById(id: string): User | null {
  const db = getDatabase();
  const stmt = db.prepare('SELECT * FROM users WHERE id = ?');
  return stmt.get(id) as User | null;
}

export function getUserByEmail(email: string): User | null {
  const db = getDatabase();
  const stmt = db.prepare('SELECT * FROM users WHERE email = ?');
  return stmt.get(email) as User | null;
}

export function getUserByUsername(username: string): User | null {
  const db = getDatabase();
  const stmt = db.prepare('SELECT * FROM users WHERE username = ?');
  return stmt.get(username) as User | null;
}

// Mission functions
export function createMission(missionData: {
  user_id: string;
  target_clicks: number;
  expires_at: string;
}): Mission {
  const db = getDatabase();
  const id = crypto.randomUUID();
  
  const stmt = db.prepare(`
    INSERT INTO missions (id, user_id, target_clicks, expires_at)
    VALUES (?, ?, ?, ?)
  `);
  
  stmt.run(id, missionData.user_id, missionData.target_clicks, missionData.expires_at);
  
  return getMissionById(id)!;
}

export function getMissionById(id: string): Mission | null {
  const db = getDatabase();
  const stmt = db.prepare('SELECT * FROM missions WHERE id = ?');
  return stmt.get(id) as Mission | null;
}

export function getUserMissions(userId: string): Mission[] {
  const db = getDatabase();
  const stmt = db.prepare('SELECT * FROM missions WHERE user_id = ? ORDER BY created_at DESC');
  return stmt.all(userId) as Mission[];
}

export function updateMission(id: string, updates: Partial<Mission>): void {
  const db = getDatabase();
  const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
  const values = Object.values(updates);
  
  const stmt = db.prepare(`UPDATE missions SET ${fields} WHERE id = ?`);
  stmt.run(...values, id);
}

// Click log functions
export function createClickLog(clickData: {
  user_id: string;
  mission_id: string;
  click_x: number;
  click_y: number;
  click_timestamp: string;
}): ClickLog {
  const db = getDatabase();
  const id = crypto.randomUUID();
  
  const stmt = db.prepare(`
    INSERT INTO click_logs (id, user_id, mission_id, click_x, click_y, click_timestamp)
    VALUES (?, ?, ?, ?, ?, ?)
  `);
  
  stmt.run(id, clickData.user_id, clickData.mission_id, clickData.click_x, clickData.click_y, clickData.click_timestamp);
  
  const getStmt = db.prepare('SELECT * FROM click_logs WHERE id = ?');
  return getStmt.get(id) as ClickLog;
}

// User stats functions
export function updateUserStats(userId: string, scoreChange: number = 0, missionCompleted: number = 0): void {
  const db = getDatabase();
  
  // Update user totals
  const updateUserStmt = db.prepare(`
    UPDATE users 
    SET total_score = total_score + ?, missions_completed = missions_completed + ?
    WHERE id = ?
  `);
  updateUserStmt.run(scoreChange, missionCompleted, userId);
  
  // Update daily stats
  const today = new Date().toISOString().split('T')[0];
  const upsertStatsStmt = db.prepare(`
    INSERT INTO user_stats (id, user_id, date, daily_score, daily_missions_completed, daily_clicks)
    VALUES (?, ?, ?, ?, ?, 1)
    ON CONFLICT(user_id, date) DO UPDATE SET
      daily_score = daily_score + ?,
      daily_missions_completed = daily_missions_completed + ?,
      daily_clicks = daily_clicks + 1
  `);
  
  const statsId = crypto.randomUUID();
  upsertStatsStmt.run(statsId, userId, today, scoreChange, missionCompleted, scoreChange, missionCompleted);
}

// Upgrade system functions
export function getUserUpgrades(userId: string): Array<{upgrade_id: string, level: number, total_spent: number}> {
  const db = getDatabase();
  const stmt = db.prepare('SELECT upgrade_id, level, total_spent FROM user_upgrades WHERE user_id = ?');
  return stmt.all(userId) as Array<{upgrade_id: string, level: number, total_spent: number}>;
}

export function purchaseUpgrade(userId: string, upgradeId: string, cost: number): boolean {
  const db = getDatabase();

  try {
    // Start transaction
    const transaction = db.transaction(() => {
      // Check if user has enough merit
      const user = db.prepare('SELECT total_score FROM users WHERE id = ?').get(userId) as {total_score: number} | undefined;
      if (!user || user.total_score < cost) {
        throw new Error('Insufficient merit');
      }

      // Deduct cost from user
      db.prepare('UPDATE users SET total_score = total_score - ? WHERE id = ?').run(cost, userId);

      // Update or insert upgrade
      const existingUpgrade = db.prepare('SELECT level, total_spent FROM user_upgrades WHERE user_id = ? AND upgrade_id = ?').get(userId, upgradeId) as {level: number, total_spent: number} | undefined;

      if (existingUpgrade) {
        db.prepare('UPDATE user_upgrades SET level = level + 1, total_spent = total_spent + ? WHERE user_id = ? AND upgrade_id = ?').run(cost, userId, upgradeId);
      } else {
        const upgradeRecordId = crypto.randomUUID();
        db.prepare('INSERT INTO user_upgrades (id, user_id, upgrade_id, level, total_spent) VALUES (?, ?, ?, 1, ?)').run(upgradeRecordId, userId, upgradeId, cost);
      }
    });

    transaction();
    return true;
  } catch (error) {
    console.error('Error purchasing upgrade:', error);
    return false;
  }
}

export function updateUserUpgradeEffects(userId: string, effects: {clickPower: number, passiveGeneration: number, missionBonus: number}): void {
  const db = getDatabase();
  db.prepare(`
    UPDATE users
    SET click_power = ?, passive_generation = ?, mission_bonus = ?
    WHERE id = ?
  `).run(effects.clickPower, effects.passiveGeneration, effects.missionBonus, userId);
}

export function calculatePassiveEarnings(userId: string): number {
  const db = getDatabase();

  // Get user's passive generation rate and last update time
  const user = db.prepare(`
    SELECT passive_generation, last_passive_update
    FROM users
    WHERE id = ?
  `).get(userId) as {passive_generation: number, last_passive_update: string} | undefined;

  if (!user || user.passive_generation <= 0) {
    return 0;
  }

  // Calculate time difference in seconds
  const lastUpdate = new Date(user.last_passive_update);
  const now = new Date();
  const timeDiffSeconds = (now.getTime() - lastUpdate.getTime()) / 1000;

  // Calculate earnings (passive_generation is per second)
  const earnings = Math.floor(user.passive_generation * timeDiffSeconds);

  return Math.max(0, earnings);
}

export function claimPassiveEarnings(userId: string): number {
  const db = getDatabase();

  const earnings = calculatePassiveEarnings(userId);

  if (earnings > 0) {
    // Add earnings to user's total score and update timestamp
    db.prepare(`
      UPDATE users
      SET total_score = total_score + ?, last_passive_update = datetime('now')
      WHERE id = ?
    `).run(earnings, userId);
  }

  return earnings;
}
