'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
// Simple audio implementation without complex generation

interface ClickEffect {
  id: string
  x: number
  y: number
  timestamp: number
}

interface WoodenFishProps {
  onFishClick: (clickData: { x: number; y: number; timestamp: number }) => void
  disabled?: boolean
  clickCount?: number
  soundEnabled?: boolean
}

export default function WoodenFish({
  onFishClick,
  disabled = false,
  clickCount = 0,
  soundEnabled = true
}: WoodenFishProps) {
  const [clickEffects, setClickEffects] = useState<ClickEffect[]>([])
  const [isPressed, setIsPressed] = useState(false)
  const fishRef = useRef<HTMLDivElement>(null)
  const clickIdRef = useRef(0)

  // Simple audio system using Web Audio API
  const audioContextRef = useRef<AudioContext | null>(null)

  // Initialize audio context
  useEffect(() => {
    if (soundEnabled && typeof window !== 'undefined') {
      try {
        audioContextRef.current = new (window.AudioContext || (window as unknown as { webkitAudioContext: typeof AudioContext }).webkitAudioContext)()
      } catch (error) {
        console.warn('Audio context not supported:', error)
      }
    }
  }, [soundEnabled])

  // Simple click sound generator
  const playClickSound = useCallback(() => {
    if (!soundEnabled || !audioContextRef.current) return

    try {
      const ctx = audioContextRef.current
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Pleasant bell-like frequency
      oscillator.frequency.setValueAtTime(800, ctx.currentTime)
      oscillator.type = 'sine'

      // Quick fade out
      gainNode.gain.setValueAtTime(0.1, ctx.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.2)

      oscillator.start(ctx.currentTime)
      oscillator.stop(ctx.currentTime + 0.2)
    } catch (error) {
      console.warn('Failed to play sound:', error)
    }
  }, [soundEnabled])

  // Clean up old click effects
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now()
      setClickEffects(prev => prev.filter(effect => now - effect.timestamp < 2000))
    }, 100)

    return () => clearInterval(interval)
  }, [])

  const handleClick = useCallback((event: React.MouseEvent | React.TouchEvent) => {
    if (disabled) return

    event.preventDefault()
    
    const rect = fishRef.current?.getBoundingClientRect()
    if (!rect) return

    // Get click/touch position
    let clientX: number, clientY: number
    
    if ('touches' in event) {
      // Touch event
      const touch = event.touches[0] || event.changedTouches[0]
      clientX = touch.clientX
      clientY = touch.clientY
    } else {
      // Mouse event
      clientX = event.clientX
      clientY = event.clientY
    }

    // Calculate relative position within the fish component
    const x = clientX - rect.left
    const y = clientY - rect.top

    const timestamp = Date.now()
    const clickId = `click-${++clickIdRef.current}-${timestamp}`

    // Add click effect
    setClickEffects(prev => [...prev, {
      id: clickId,
      x,
      y,
      timestamp
    }])

    // Play click sound
    playClickSound()

    // Call the parent callback
    onFishClick({ x, y, timestamp })
  }, [disabled, onFishClick, playClickSound])

  const handleMouseDown = useCallback(() => {
    if (!disabled) setIsPressed(true)
  }, [disabled])

  const handleMouseUp = useCallback(() => {
    setIsPressed(false)
  }, [])

  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    if (!disabled) {
      setIsPressed(true)
      // Prevent default to avoid mouse events on touch devices
      event.preventDefault()
    }
  }, [disabled])

  const handleTouchEnd = useCallback((event: React.TouchEvent) => {
    setIsPressed(false)
    // Handle the click on touch end
    handleClick(event)
  }, [handleClick])

  return (
    <div className="relative flex items-center justify-center">
      {/* Wooden Fish Container */}
      <motion.div
        ref={fishRef}
        className={`
          relative select-none cursor-pointer
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}
          transition-opacity duration-200
        `}
        animate={{
          scale: isPressed ? 0.95 : 1,
          rotate: isPressed ? -2 : 0
        }}
        transition={{
          type: "spring",
          stiffness: 400,
          damping: 25
        }}
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        style={{
          touchAction: 'manipulation' // Prevent double-tap zoom on mobile
        }}
      >
        {/* Wooden Fish Emoji */}
        <div className="text-8xl sm:text-9xl md:text-[12rem] leading-none">
          🪵🐟
        </div>

        {/* Glow Effect */}
        <motion.div
          className="absolute inset-0 rounded-full"
          animate={{
            boxShadow: isPressed 
              ? '0 0 30px rgba(245, 158, 11, 0.6), 0 0 60px rgba(245, 158, 11, 0.3)'
              : '0 0 20px rgba(245, 158, 11, 0.3)'
          }}
          transition={{ duration: 0.2 }}
        />

        {/* Click Effects */}
        <AnimatePresence>
          {clickEffects.map((effect) => (
            <motion.div
              key={effect.id}
              className="absolute pointer-events-none z-10"
              style={{
                left: effect.x,
                top: effect.y,
                transform: 'translate(-50%, -50%)'
              }}
              initial={{
                opacity: 1,
                scale: 0.5,
                y: 0
              }}
              animate={{
                opacity: 0,
                scale: 1.5,
                y: -50
              }}
              exit={{
                opacity: 0,
                scale: 0.5
              }}
              transition={{
                duration: 1.5,
                ease: "easeOut"
              }}
            >
              <div className="text-2xl font-bold text-amber-600 drop-shadow-lg">
                功德+1
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Click Counter Display */}
      {clickCount > 0 && (
        <motion.div
          className="absolute -bottom-8 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm font-medium">
            Clicks: {clickCount}
          </div>
        </motion.div>
      )}

      {/* Instructions */}
      <motion.div
        className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <p className="text-sm text-amber-600 font-medium">
          {disabled ? 'Wooden fish is resting...' : 'Click or tap to gain merit!'}
        </p>
      </motion.div>
    </div>
  )
}
