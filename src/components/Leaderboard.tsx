'use client'

import { useState, useEffect } from 'react'
import { Trophy, Users, Medal, Crown, Star } from 'lucide-react'

interface UserLeaderboardEntry {
  id: string
  username: string
  display_name: string
  clan_district: string
  total_score: number
  missions_completed: number
  rank: number
}

interface ClanLeaderboardEntry {
  clan_district: string
  total_score: number
  member_count: number
  avg_score: number
  rank: number
}

interface LeaderboardData {
  userLeaderboard: UserLeaderboardEntry[]
  clanLeaderboard: ClanLeaderboardEntry[]
  currentUser?: {
    rank: number
    total_score: number
    clan_district: string
  }
}

export default function Leaderboard() {
  const [data, setData] = useState<LeaderboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'users' | 'clans'>('clans')

  useEffect(() => {
    fetchLeaderboard()
    
    // Refresh leaderboard every 2 minutes
    const interval = setInterval(fetchLeaderboard, 120000)
    
    return () => clearInterval(interval)
  }, [])

  const fetchLeaderboard = async () => {
    try {
      const response = await fetch('/api/leaderboard')
      
      if (!response.ok) {
        throw new Error('Failed to fetch leaderboard')
      }
      
      const leaderboardData: LeaderboardData = await response.json()
      setData(leaderboardData)
      setError(null)
    } catch (err) {
      console.error('Error fetching leaderboard:', err)
      setError('Failed to load leaderboard')
    } finally {
      setLoading(false)
    }
  }

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-5 h-5 text-yellow-500" />
      case 2:
        return <Medal className="w-5 h-5 text-gray-400" />
      case 3:
        return <Medal className="w-5 h-5 text-amber-600" />
      default:
        return <span className="w-5 h-5 flex items-center justify-center text-sm font-bold text-gray-600">#{rank}</span>
    }
  }

  const formatDistrictName = (district: string) => {
    return district.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  if (loading) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
        <div className="animate-pulse">
          <div className="h-6 bg-amber-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-amber-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
        <div className="text-center text-red-600">
          <Trophy className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p>{error || 'Failed to load leaderboard'}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-800 flex items-center">
          <Trophy className="w-6 h-6 text-amber-600 mr-2" />
          Leaderboard
        </h2>
        
        {/* Tab Switcher */}
        <div className="flex bg-amber-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('clans')}
            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'clans'
                ? 'bg-white text-amber-800 shadow-sm'
                : 'text-amber-600 hover:text-amber-800'
            }`}
          >
            Districts
          </button>
          <button
            onClick={() => setActiveTab('users')}
            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'users'
                ? 'bg-white text-amber-800 shadow-sm'
                : 'text-amber-600 hover:text-amber-800'
            }`}
          >
            Players
          </button>
        </div>
      </div>

      {/* Current User Stats */}
      {data.currentUser && (
        <div className="mb-6 p-3 bg-amber-50 rounded-lg border border-amber-200">
          <div className="flex items-center justify-between text-sm">
            <span className="text-amber-700">Your Rank:</span>
            <div className="flex items-center space-x-2">
              {getRankIcon(data.currentUser.rank)}
              <span className="font-medium text-amber-800">
                #{data.currentUser.rank} • {data.currentUser.total_score} merit
              </span>
            </div>
          </div>
          <div className="flex items-center justify-between text-sm mt-1">
            <span className="text-amber-700">Your District:</span>
            <span className="font-medium text-amber-800">
              {formatDistrictName(data.currentUser.clan_district)}
            </span>
          </div>
        </div>
      )}

      {/* Clan Leaderboard */}
      {activeTab === 'clans' && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-600 mb-3 flex items-center">
            <Users className="w-4 h-4 mr-1" />
            Hong Kong Districts
          </h3>
          {data.clanLeaderboard.slice(0, 10).map((clan) => (
            <div
              key={clan.clan_district}
              className={`flex items-center justify-between p-3 rounded-lg border transition-colors ${
                data.currentUser?.clan_district === clan.clan_district
                  ? 'bg-amber-50 border-amber-300'
                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center space-x-3">
                {getRankIcon(clan.rank)}
                <div>
                  <div className="font-medium text-gray-800">
                    {formatDistrictName(clan.clan_district)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {clan.member_count} members • Avg: {clan.avg_score}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-amber-600">
                  {clan.total_score.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">merit</div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* User Leaderboard */}
      {activeTab === 'users' && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-600 mb-3 flex items-center">
            <Star className="w-4 h-4 mr-1" />
            Top Players
          </h3>
          {data.userLeaderboard.slice(0, 10).map((user) => (
            <div
              key={user.id}
              className="flex items-center justify-between p-3 rounded-lg border bg-gray-50 border-gray-200 hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center space-x-3">
                {getRankIcon(user.rank)}
                <div>
                  <div className="font-medium text-gray-800">
                    {user.display_name || user.username}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatDistrictName(user.clan_district)} • {user.missions_completed} missions
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-amber-600">
                  {user.total_score.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">merit</div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
