# 木魚功德 (Mokugyo Merit) 🪵🐟

A traditional mokugyo clicking game with modern web technology, featuring missions, clan competition, and anti-cheat mechanisms.

## Features

- **Interactive 木魚 (Mokugyo)**: Click or touch the mokugyo to gain merit points
- **Mission System**: 3 time-based missions per hour (active 08:00-22:59)
- **Anti-Cheat Protection**: Server-side validation prevents automated clicking
- **Clan System**: Choose from Hong Kong's 18 districts as your clan
- **Real-time Leaderboards**: Global and clan-based rankings
- **User Authentication**: Secure login with profile management
- **Login Streaks**: Track consecutive login days
- **Responsive Design**: Works on desktop and mobile devices

## Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Authentication, Real-time)
- **Animations**: Framer Motion
- **Icons**: Lucide React

## Setup Instructions

### 1. <PERSON><PERSON> and Install

```bash
git clone <repository-url>
cd meme01
npm install
```

### 2. Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Wait for the project to be fully initialized

### 3. Set up Database Schema

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `supabase-schema.sql` into the editor
4. Run the SQL commands to create all necessary tables, functions, and policies

### 4. Configure Environment Variables

1. Copy `.env.local` and update with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

You can find these values in your Supabase project settings under "API".

### 5. Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## Game Rules

### Mission System

- **Active Hours**: Missions are only available between 08:00-22:59 (Hong Kong time)
- **Mission Frequency**: 3 new missions are generated every hour
- **Click Limits**: Each mission requires 1-60 clicks to complete
- **Penalty System**: Exceeding the required clicks results in -100 points
- **Anti-Cheat**: Server-side validation prevents automated clicking

### Scoring

- **Merit Points**: Gained by completing missions successfully
- **Penalties**: -100 points for exceeding mission click requirements
- **Leaderboards**: Rankings updated in real-time

### Clans (Hong Kong Districts)

Choose from 18 Hong Kong districts:
- Central and Western, Eastern, Southern, Wan Chai
- Sham Shui Po, Kowloon City, Kwun Tong, Wong Tai Sin, Yau Tsim Mong
- Islands, Kwai Tsing, North, Sai Kung, Sha Tin, Tai Po, Tsuen Wan, Tuen Mun, Yuen Long

## Database Schema

The application uses the following main tables:

- **users**: User profiles with clan affiliation and stats
- **missions**: Time-based missions with click requirements
- **user_stats**: Cached user statistics for performance
- **click_logs**: Anti-cheat click tracking
- **leaderboard**: View for rankings

## Development Status

This project is currently in development. Completed features:

- [x] Project setup with Next.js and Supabase
- [x] Database schema design
- [x] Basic UI layout and responsive design
- [x] User authentication system
- [x] Clan selection system

Upcoming features:

- [x] Interactive mokugyo component
- [x] Animation system for merit points
- [x] Sound effects
- [x] Mission system logic
- [x] Real-time scoring
- [ ] District leaderboards
- [x] Mobile optimization
- [ ] Cookie Clicker-style idle mechanics
- [ ] Upgrade system
- [ ] Achievement system

## Contributing

This is a personal project showcasing traditional Chinese culture through modern web technology. Feel free to explore the code and suggest improvements!

## License

MIT License - see LICENSE file for details.
