import { format, startOfHour, addHours, parseISO } from 'date-fns'

export interface Mission {
  id: string
  user_id: string
  mission_type: 'hourly'
  target_clicks: number
  completed_clicks: number
  status: 'active' | 'completed' | 'failed'
  created_at: string
  expires_at: string
  score_awarded: number
}

export interface MissionProgress {
  currentMission: Mission | null
  clicksRemaining: number
  timeRemaining: number
  canClick: boolean
  penalty: number
}

// Hong Kong timezone offset (UTC+8)
const HK_TIMEZONE_OFFSET = 8 * 60 * 60 * 1000

// Get current Hong Kong time
export function getHKTime(): Date {
  const now = new Date()
  return new Date(now.getTime() + HK_TIMEZONE_OFFSET)
}

// Check if current time is within active hours (08:00-22:59 HK time)
export function isWithinActiveHours(date: Date = getHKTime()): boolean {
  const hkTime = new Date(date.getTime() + HK_TIMEZONE_OFFSET)
  const hour = hkTime.getHours()
  return hour >= 8 && hour <= 22
}

// Get the current hour slot for mission generation
export function getCurrentHourSlot(date: Date = getHKTime()): string {
  const hkTime = new Date(date.getTime() + HK_TIMEZONE_OFFSET)
  const hourStart = startOfHour(hkTime)
  return format(hourStart, 'yyyy-MM-dd HH:00:00')
}

// Generate mission for a specific hour slot
export function generateMissionForHour(userId: string, hourSlot: string, missionIndex: number): Omit<Mission, 'id'> {
  const hourStart = parseISO(hourSlot.replace(' ', 'T') + 'Z')
  const hourEnd = addHours(hourStart, 1)
  
  // Generate target clicks (1-60 based on mission index and hour)
  const baseClicks = 10 + (missionIndex * 15) // 10, 25, 40 for missions 1, 2, 3
  const hourVariation = Math.floor(Math.random() * 21) - 10 // -10 to +10
  const targetClicks = Math.max(1, Math.min(60, baseClicks + hourVariation))

  return {
    user_id: userId,
    mission_type: 'hourly',
    target_clicks: targetClicks,
    completed_clicks: 0,
    status: 'active',
    created_at: new Date().toISOString(),
    expires_at: hourEnd.toISOString(),
    score_awarded: 0
  }
}

// Calculate score for completed mission
export function calculateMissionScore(mission: Mission): number {
  if (mission.status !== 'completed') return 0
  
  const baseScore = mission.target_clicks
  const efficiency = mission.completed_clicks === mission.target_clicks ? 1.5 : 1.0
  
  return Math.floor(baseScore * efficiency)
}

// Calculate penalty for exceeding clicks
export function calculateClickPenalty(targetClicks: number, actualClicks: number): number {
  if (actualClicks <= targetClicks) return 0
  return -100 // Fixed penalty for exceeding target
}

// Validate click against mission requirements
export function validateClick(mission: Mission, newClickCount: number): {
  isValid: boolean
  newStatus: Mission['status']
  penalty: number
  scoreAwarded: number
} {
  if (!mission || mission.status !== 'active') {
    return {
      isValid: false,
      newStatus: mission?.status || 'failed',
      penalty: 0,
      scoreAwarded: 0
    }
  }

  // Check if mission has expired
  const now = new Date()
  const expiresAt = new Date(mission.expires_at)
  if (now > expiresAt) {
    return {
      isValid: false,
      newStatus: 'failed',
      penalty: 0,
      scoreAwarded: 0
    }
  }

  const updatedMission = { ...mission, completed_clicks: newClickCount }

  // Check if mission is completed
  if (newClickCount >= mission.target_clicks) {
    const penalty = calculateClickPenalty(mission.target_clicks, newClickCount)
    const baseScore = calculateMissionScore({ ...updatedMission, status: 'completed' })
    const scoreAwarded = Math.max(0, baseScore + penalty)

    return {
      isValid: true,
      newStatus: 'completed',
      penalty,
      scoreAwarded
    }
  }

  // Mission still in progress
  return {
    isValid: true,
    newStatus: 'active',
    penalty: 0,
    scoreAwarded: 0
  }
}

// Get missions that should be generated for current hour
export function getMissionsForCurrentHour(userId: string): Array<Omit<Mission, 'id'>> {
  if (!isWithinActiveHours()) {
    return []
  }

  const currentHourSlot = getCurrentHourSlot()
  const missions: Array<Omit<Mission, 'id'>> = []

  // Generate 3 missions per hour
  for (let i = 0; i < 3; i++) {
    missions.push(generateMissionForHour(userId, currentHourSlot, i))
  }

  return missions
}

// Format time remaining for display
export function formatTimeRemaining(expiresAt: string): string {
  const now = new Date()
  const expires = new Date(expiresAt)
  const diff = expires.getTime() - now.getTime()

  if (diff <= 0) return 'Expired'

  const minutes = Math.floor(diff / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)

  if (minutes > 0) {
    return `${minutes}m ${seconds}s`
  }
  return `${seconds}s`
}

// Get next mission generation time
export function getNextMissionTime(): Date {
  const hkTime = getHKTime()
  const currentHour = hkTime.getHours()
  
  // If before 8 AM, next missions at 8 AM
  if (currentHour < 8) {
    const nextMissionTime = new Date(hkTime)
    nextMissionTime.setHours(8, 0, 0, 0)
    return nextMissionTime
  }
  
  // If after 10 PM, next missions at 8 AM tomorrow
  if (currentHour >= 23) {
    const nextMissionTime = new Date(hkTime)
    nextMissionTime.setDate(nextMissionTime.getDate() + 1)
    nextMissionTime.setHours(8, 0, 0, 0)
    return nextMissionTime
  }
  
  // Otherwise, next hour
  const nextMissionTime = new Date(hkTime)
  nextMissionTime.setHours(currentHour + 1, 0, 0, 0)
  return nextMissionTime
}
