'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import GameLayout from '@/components/GameLayout'
import Mokugyo from '@/components/Mokugyo'
import MissionPanel from '@/components/MissionPanel'
import Leaderboard from '@/components/Leaderboard'
import Upgrades from '@/components/Upgrades'
import PassiveIncome from '@/components/PassiveIncome'
import { Mission } from '@/lib/missionUtils'

export default function Home() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [clickCount, setClickCount] = useState(0)
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [currentMission, setCurrentMission] = useState<Mission | null>(null)
  const [missionLoading, setMissionLoading] = useState(false)
  const [totalScore, setTotalScore] = useState(0)
  const [clickPower, setClickPower] = useState(1)
  const [passiveGeneration, setPassiveGeneration] = useState(0)
  const [activeTab, setActiveTab] = useState<'game' | 'upgrades'>('game')

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  // Load upgrade effects and passive earnings
  useEffect(() => {
    if (user) {
      loadUpgradeEffects()
    }
  }, [user])

  const loadUpgradeEffects = async () => {
    try {
      const response = await fetch('/api/upgrades')
      if (response.ok) {
        const data = await response.json()
        setClickPower(data.effects.clickPower || 1)
        setPassiveGeneration(data.effects.passiveGeneration || 0)
        setTotalScore(prev => prev + (data.passiveEarnings || 0))
      }
    } catch (error) {
      console.error('Error loading upgrade effects:', error)
    }
  }

  const handleMokugyoClick = async (clickData: { x: number; y: number; timestamp: number }) => {
    console.log('Mokugyo clicked:', clickData)
    setClickCount(prev => prev + clickPower)

    // Send click to server for mission validation
    if (currentMission && currentMission.status === 'active') {
      setMissionLoading(true)

      try {
        const response = await fetch('/api/clicks', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            missionId: currentMission.id,
            clickData
          })
        })

        if (response.ok) {
          const result = await response.json()
          console.log('Click processed:', result)

          // Update current mission with the latest data from server
          if (result.mission) {
            setCurrentMission(result.mission)
            setClickCount(result.mission.completed_clicks)
          }
        } else {
          console.error('Failed to process click:', await response.text())
        }
      } catch (error) {
        console.error('Error processing click:', error)
      } finally {
        setMissionLoading(false)
      }
    }
  }

  const handleUpgradePurchase = async (upgradeId: string, cost: number): Promise<boolean> => {
    try {
      const response = await fetch('/api/upgrades', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ upgradeId })
      })

      if (response.ok) {
        const data = await response.json()
        setTotalScore(data.remainingMerit)
        setClickPower(data.effects.clickPower || 1)
        setPassiveGeneration(data.effects.passiveGeneration || 0)
        return true
      }
      return false
    } catch (error) {
      console.error('Error purchasing upgrade:', error)
      return false
    }
  }

  const handleClaimPassiveEarnings = async (): Promise<number> => {
    try {
      const response = await fetch('/api/upgrades')
      if (response.ok) {
        const data = await response.json()
        const earnings = data.passiveEarnings || 0
        setTotalScore(prev => prev + earnings)
        return earnings
      }
      return 0
    } catch (error) {
      console.error('Error claiming passive earnings:', error)
      return 0
    }
  }

  const handleMissionSelect = (mission: Mission | null) => {
    setCurrentMission(mission)
    if (mission) {
      setClickCount(mission.completed_clicks)
    }
  }





  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🪵🐟</div>
          <div className="text-lg text-amber-800 font-medium">Loading...</div>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🪵🐟</div>
          <div className="text-lg text-amber-800 font-medium">Redirecting to login...</div>
        </div>
      </div>
    )
  }

  return (
    <GameLayout user={user}>
      <div className="flex-1 flex flex-col items-center justify-center p-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-amber-800 mb-2">
            木魚功德
          </h1>
          <p className="text-lg text-amber-600">
            Mokugyo Merit Game
          </p>
          <div className="mt-4 flex items-center justify-center space-x-4 text-sm">
            <div className="bg-white/80 px-3 py-1 rounded-full border border-amber-200">
              Merit: <span className="font-bold text-amber-800">{totalScore.toLocaleString()}</span>
            </div>
            <div className="bg-white/80 px-3 py-1 rounded-full border border-amber-200">
              Click Power: <span className="font-bold text-blue-600">{clickPower}</span>
            </div>
            {passiveGeneration > 0 && (
              <div className="bg-white/80 px-3 py-1 rounded-full border border-amber-200">
                Passive: <span className="font-bold text-green-600">{passiveGeneration.toFixed(1)}/sec</span>
              </div>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-amber-200 mb-8">
          <button
            onClick={() => setActiveTab('game')}
            className={`px-6 py-2 rounded-xl font-medium transition-colors ${
              activeTab === 'game'
                ? 'bg-amber-500 text-white shadow-md'
                : 'text-amber-700 hover:text-amber-800'
            }`}
          >
            🪵 Game
          </button>
          <button
            onClick={() => setActiveTab('upgrades')}
            className={`px-6 py-2 rounded-xl font-medium transition-colors ${
              activeTab === 'upgrades'
                ? 'bg-amber-500 text-white shadow-md'
                : 'text-amber-700 hover:text-amber-800'
            }`}
          >
            🛒 Upgrades
          </button>
        </div>

        {/* Game Tab Content */}
        {activeTab === 'game' && (
          <div className="w-full max-w-6xl space-y-8">
            {/* Interactive Mokugyo */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-amber-200">
          <div className="flex justify-end mb-4">
            <button
              onClick={() => setSoundEnabled(!soundEnabled)}
              className={`
                px-3 py-1 rounded-full text-sm font-medium transition-colors
                ${soundEnabled
                  ? 'bg-amber-100 text-amber-800 hover:bg-amber-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }
              `}
            >
              {soundEnabled ? '🔊 Sound On' : '🔇 Sound Off'}
            </button>
          </div>

          <Mokugyo
            onMokugyoClick={handleMokugyoClick}
            clickCount={clickCount}
            soundEnabled={soundEnabled}
            disabled={missionLoading || !currentMission || currentMission.status !== 'active'}
          />
        </div>

        {/* Mission Panel */}
        <div className="mt-8 w-full max-w-4xl">
          <MissionPanel
            onMissionSelect={handleMissionSelect}
          />
        </div>

        {/* Mission Status */}
        {currentMission && (
          <div className="mt-6 text-center">
            <div className="text-sm text-amber-700">
              <strong>Current Mission:</strong> {currentMission.completed_clicks}/{currentMission.target_clicks} clicks
            </div>
            {currentMission.completed_clicks >= currentMission.target_clicks && (
              <div className="text-sm text-red-600 font-medium mt-1">
                ⚠️ Exceeding target will result in -100 penalty!
              </div>
            )}
          </div>
        )}

        {!currentMission && (
          <div className="mt-6 text-center text-amber-600">
            Select an active mission to start clicking the mokugyo
          </div>
        )}

            {/* Leaderboard */}
            <div className="w-full max-w-4xl">
              <Leaderboard />
            </div>

            {/* Passive Income Display */}
            {passiveGeneration > 0 && (
              <div className="w-full max-w-2xl">
                <PassiveIncome
                  passiveGeneration={passiveGeneration}
                  onClaimEarnings={handleClaimPassiveEarnings}
                />
              </div>
            )}
          </div>
        )}

        {/* Upgrades Tab Content */}
        {activeTab === 'upgrades' && (
          <div className="w-full max-w-6xl">
            <Upgrades
              userMerit={totalScore}
              onUpgradePurchase={handleUpgradePurchase}
            />
          </div>
        )}
      </div>
    </GameLayout>
  )
}
