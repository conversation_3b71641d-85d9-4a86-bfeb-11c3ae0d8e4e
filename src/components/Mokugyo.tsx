'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
// Simple audio implementation without complex generation

interface ClickEffect {
  id: string
  x: number
  y: number
  timestamp: number
}

interface MokugyoProps {
  onMokugyoClick: (clickData: { x: number; y: number; timestamp: number }) => void
  disabled?: boolean
  clickCount?: number
  soundEnabled?: boolean
}

export default function Mokugyo({
  onMokugyoClick,
  disabled = false,
  clickCount = 0,
  soundEnabled = true
}: MokugyoProps) {
  const [clickEffects, setClickEffects] = useState<ClickEffect[]>([])
  const [isPressed, setIsPressed] = useState(false)
  const mokugyoRef = useRef<HTMLDivElement>(null)
  const clickIdRef = useRef(0)

  // Simple audio system using Web Audio API
  const audioContextRef = useRef<AudioContext | null>(null)

  // Initialize audio context
  useEffect(() => {
    if (soundEnabled && typeof window !== 'undefined') {
      try {
        audioContextRef.current = new (window.AudioContext || (window as unknown as { webkitAudioContext: typeof AudioContext }).webkitAudioContext)()
      } catch (error) {
        console.warn('Audio context not supported:', error)
      }
    }
  }, [soundEnabled])

  // Clean up click effects
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = Date.now()
      setClickEffects(prev => prev.filter(effect => now - effect.timestamp < 2000))
    }, 1000)

    return () => clearInterval(cleanup)
  }, [])

  // Play click sound using Web Audio API
  const playClickSound = useCallback(() => {
    if (!soundEnabled || !audioContextRef.current) return

    try {
      const ctx = audioContextRef.current
      
      // Resume context if suspended (required for some browsers)
      if (ctx.state === 'suspended') {
        ctx.resume()
      }

      // Create a simple wooden percussion sound
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()
      const filterNode = ctx.createBiquadFilter()

      // Connect nodes
      oscillator.connect(filterNode)
      filterNode.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Configure wooden percussion sound
      oscillator.type = 'triangle'
      oscillator.frequency.setValueAtTime(200, ctx.currentTime)
      oscillator.frequency.exponentialRampToValueAtTime(80, ctx.currentTime + 0.1)

      // Low-pass filter for wooden tone
      filterNode.type = 'lowpass'
      filterNode.frequency.setValueAtTime(800, ctx.currentTime)

      // Quick attack and decay for percussion
      gainNode.gain.setValueAtTime(0, ctx.currentTime)
      gainNode.gain.linearRampToValueAtTime(0.3, ctx.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.15)

      // Play the sound
      oscillator.start(ctx.currentTime)
      oscillator.stop(ctx.currentTime + 0.15)

    } catch (error) {
      console.warn('Error playing click sound:', error)
    }
  }, [soundEnabled])

  const handleClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (disabled) return

    const rect = mokugyoRef.current?.getBoundingClientRect()
    if (!rect) return

    const { clientX, clientY } = event

    // Calculate relative position within the mokugyo component
    const x = clientX - rect.left
    const y = clientY - rect.top

    const timestamp = Date.now()
    const clickId = `click-${++clickIdRef.current}-${timestamp}`

    // Add click effect
    setClickEffects(prev => [...prev, {
      id: clickId,
      x,
      y,
      timestamp
    }])

    // Play click sound
    playClickSound()

    // Call the parent callback
    onMokugyoClick({ x, y, timestamp })
  }, [disabled, onMokugyoClick, playClickSound])

  const handleMouseDown = useCallback(() => {
    if (!disabled) setIsPressed(true)
  }, [disabled])

  const handleMouseUp = useCallback(() => {
    setIsPressed(false)
  }, [])

  const handleTouchStart = useCallback((event: React.TouchEvent<HTMLDivElement>) => {
    if (disabled) return
    
    event.preventDefault() // Prevent scrolling
    setIsPressed(true)
  }, [disabled])

  const handleTouchEnd = useCallback((event: React.TouchEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsPressed(false)
    
    // Trigger click on touch end
    if (!disabled && event.changedTouches.length > 0) {
      const touch = event.changedTouches[0]
      const rect = mokugyoRef.current?.getBoundingClientRect()
      if (rect) {
        const x = touch.clientX - rect.left
        const y = touch.clientY - rect.top
        const timestamp = Date.now()
        
        setClickEffects(prev => [...prev, {
          id: `touch-${++clickIdRef.current}-${timestamp}`,
          x,
          y,
          timestamp
        }])
        
        playClickSound()
        onMokugyoClick({ x, y, timestamp })
      }
    }
  }, [disabled, onMokugyoClick, playClickSound])

  return (
    <div className="relative flex items-center justify-center">
      {/* Mokugyo Container */}
      <motion.div
        ref={mokugyoRef}
        className={`
          relative select-none cursor-pointer
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}
          transition-opacity duration-200
        `}
        animate={{
          scale: isPressed ? 0.95 : 1,
          rotate: isPressed ? -2 : 0
        }}
        transition={{
          type: "spring",
          stiffness: 400,
          damping: 25
        }}
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        style={{
          touchAction: 'manipulation' // Prevent double-tap zoom on mobile
        }}
      >
        {/* Mokugyo - Traditional Wooden Fish Design */}
        <div className="relative w-40 h-32 sm:w-48 sm:h-36 md:w-56 md:h-40 mx-auto">
          {/* Main Fish Body - Oval Shape */}
          <div className="absolute inset-0 bg-gradient-to-br from-amber-600 via-amber-700 to-amber-800 shadow-2xl border-4 border-amber-900"
               style={{
                 borderRadius: '50% 50% 50% 50% / 60% 60% 40% 40%',
                 transform: 'rotate(-10deg)'
               }}>
            {/* Wood grain effects */}
            <div className="absolute inset-2 bg-gradient-to-r from-amber-500/30 to-transparent"
                 style={{ borderRadius: '50% 50% 50% 50% / 60% 60% 40% 40%' }}></div>
            <div className="absolute inset-4 bg-gradient-to-l from-amber-400/20 to-transparent"
                 style={{ borderRadius: '50% 50% 50% 50% / 60% 60% 40% 40%' }}></div>

            {/* Fish Eye */}
            <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-amber-900 rounded-full shadow-inner"></div>
            <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-amber-200 rounded-full ml-1 mt-1"></div>

            {/* Fish Mouth */}
            <div className="absolute top-1/2 left-2 w-4 h-1 bg-amber-900 rounded-full opacity-60"></div>

            {/* Wood grain lines */}
            <div className="absolute top-1/3 left-1/2 w-8 h-0.5 bg-amber-900 rounded opacity-30 transform -rotate-12"></div>
            <div className="absolute bottom-1/3 left-1/3 w-10 h-0.5 bg-amber-900 rounded opacity-25 transform rotate-6"></div>
            <div className="absolute top-2/3 left-2/3 w-6 h-0.5 bg-amber-900 rounded opacity-20 transform -rotate-8"></div>
          </div>

          {/* Fish Tail */}
          <div className="absolute -right-2 top-1/2 transform -translate-y-1/2 rotate-45">
            <div className="w-6 h-6 bg-gradient-to-br from-amber-600 to-amber-800 border-2 border-amber-900"
                 style={{
                   clipPath: 'polygon(0% 0%, 100% 50%, 0% 100%)',
                   transform: 'rotate(-45deg)'
                 }}></div>
          </div>

          {/* Highlight effect */}
          <div className="absolute top-2 left-4 w-8 h-4 bg-amber-300/40 rounded-full blur-sm transform rotate-12"></div>
        </div>

        {/* Click Effects */}
        <AnimatePresence>
          {clickEffects.map((effect) => (
            <motion.div
              key={effect.id}
              className="absolute pointer-events-none text-2xl font-bold text-amber-600"
              style={{
                left: effect.x,
                top: effect.y,
                transform: 'translate(-50%, -50%)'
              }}
              initial={{ opacity: 1, scale: 0.5, y: 0 }}
              animate={{ 
                opacity: 0, 
                scale: 1.5, 
                y: -50,
                rotate: Math.random() * 40 - 20
              }}
              exit={{ opacity: 0 }}
              transition={{ 
                duration: 1.5,
                ease: "easeOut"
              }}
            >
              功德+1
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Click Counter */}
      <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="bg-amber-100 px-4 py-2 rounded-full border border-amber-300">
          <span className="text-sm font-medium text-amber-800">
            Clicks: {clickCount}
          </span>
        </div>
      </div>

      {/* Instructions */}
      <div className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 text-center">
        <p className="text-xs text-amber-600 max-w-xs">
          {disabled ? 'Select an active mission to start clicking' : 'Click or tap to gain merit'}
        </p>
      </div>
    </div>
  )
}
