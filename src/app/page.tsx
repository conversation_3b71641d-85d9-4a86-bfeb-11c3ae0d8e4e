'use client'

import { useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import GameLayout from '@/components/GameLayout'
import AuthForm from '@/components/AuthForm'
import SetupNotice from '@/components/SetupNotice'
import WoodenFish from '@/components/WoodenFish'
import MissionPanel from '@/components/MissionPanel'
import { Mission } from '@/lib/missionUtils'

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [clickCount, setClickCount] = useState(0)
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [currentMission, setCurrentMission] = useState<Mission | null>(null)
  const [missionLoading, setMissionLoading] = useState(false)

  useEffect(() => {
    if (!supabase) {
      setLoading(false)
      return
    }

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const handleFishClick = async (clickData: { x: number; y: number; timestamp: number }) => {
    console.log('Fish clicked:', clickData)
    setClickCount(prev => prev + 1)

    // Send click to server for mission validation
    if (currentMission && currentMission.status === 'active') {
      setMissionLoading(true)

      try {
        const response = await fetch('/api/clicks', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            missionId: currentMission.id,
            clickData
          })
        })

        if (response.ok) {
          const result = await response.json()
          console.log('Click processed:', result)

          // Update mission state will be handled by MissionPanel
        } else {
          console.error('Failed to process click:', await response.text())
        }
      } catch (error) {
        console.error('Error processing click:', error)
      } finally {
        setMissionLoading(false)
      }
    }
  }

  const handleMissionSelect = (mission: Mission | null) => {
    setCurrentMission(mission)
    if (mission) {
      setClickCount(mission.completed_clicks)
    }
  }

  const handleMissionUpdate = (mission: Mission) => {
    if (currentMission?.id === mission.id) {
      setCurrentMission(mission)
      setClickCount(mission.completed_clicks)
    }
  }

  // Show setup notice if Supabase is not configured
  if (!supabase) {
    return <SetupNotice />
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🪵🐟</div>
          <div className="text-lg text-amber-800 font-medium">Loading...</div>
        </div>
      </div>
    )
  }

  if (!user) {
    return <AuthForm />
  }

  return (
    <GameLayout user={user}>
      <div className="flex-1 flex flex-col items-center justify-center p-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-amber-800 mb-2">
            木魚功德
          </h1>
          <p className="text-lg text-amber-600">
            Wooden Fish Merit Game
          </p>
        </div>

        {/* Interactive Wooden Fish */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-amber-200">
          <div className="flex justify-end mb-4">
            <button
              onClick={() => setSoundEnabled(!soundEnabled)}
              className={`
                px-3 py-1 rounded-full text-sm font-medium transition-colors
                ${soundEnabled
                  ? 'bg-amber-100 text-amber-800 hover:bg-amber-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }
              `}
            >
              {soundEnabled ? '🔊 Sound On' : '🔇 Sound Off'}
            </button>
          </div>

          <WoodenFish
            onFishClick={handleFishClick}
            clickCount={clickCount}
            soundEnabled={soundEnabled}
            disabled={missionLoading || !currentMission || currentMission.status !== 'active'}
          />
        </div>

        {/* Mission Panel */}
        <div className="mt-8 w-full max-w-4xl">
          <MissionPanel
            userId={user.id}
            onMissionSelect={handleMissionSelect}
            onMissionUpdate={handleMissionUpdate}
          />
        </div>

        {/* Mission Status */}
        {currentMission && (
          <div className="mt-6 text-center">
            <div className="text-sm text-amber-700">
              <strong>Current Mission:</strong> {currentMission.completed_clicks}/{currentMission.target_clicks} clicks
            </div>
            {currentMission.completed_clicks >= currentMission.target_clicks && (
              <div className="text-sm text-red-600 font-medium mt-1">
                ⚠️ Exceeding target will result in -100 penalty!
              </div>
            )}
          </div>
        )}

        {!currentMission && (
          <div className="mt-6 text-center text-amber-600">
            Select an active mission to start clicking
          </div>
        )}
      </div>
    </GameLayout>
  )
}
