{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/lib/types.ts"], "sourcesContent": ["// Hong Kong Districts\nexport const HK_DISTRICTS = [\n  'central_western', 'eastern', 'southern', 'wan_chai',\n  'sham_shui_po', 'kowloon_city', 'kwun_tong', 'wong_tai_sin', 'yau_tsim_mong',\n  'islands', 'kwai_tsing', 'north', 'sai_kung', 'sha_tin', 'tai_po', 'tsuen_wan', 'tuen_mun', 'yuen_long'\n] as const;\n\nexport type HKDistrict = typeof HK_DISTRICTS[number];\n\nexport interface User {\n  id: string;\n  username: string;\n  email: string;\n  password_hash: string;\n  display_name: string;\n  clan_district: HKDistrict;\n  total_score: number;\n  missions_completed: number;\n  login_streak: number;\n  last_login_date: string | null;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Mission {\n  id: string;\n  user_id: string;\n  mission_type: string;\n  target_clicks: number;\n  completed_clicks: number;\n  status: 'active' | 'completed' | 'failed';\n  score_awarded: number;\n  created_at: string;\n  expires_at: string;\n  updated_at: string;\n}\n\nexport interface ClickLog {\n  id: string;\n  user_id: string;\n  mission_id: string;\n  click_x: number;\n  click_y: number;\n  click_timestamp: string;\n  server_timestamp: string;\n  created_at: string;\n}\n\nexport interface UserStats {\n  id: string;\n  user_id: string;\n  date: string;\n  daily_score: number;\n  daily_missions_completed: number;\n  daily_clicks: number;\n  created_at: string;\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACf,MAAM,eAAe;IAC1B;IAAmB;IAAW;IAAY;IAC1C;IAAgB;IAAgB;IAAa;IAAgB;IAC7D;IAAW;IAAc;IAAS;IAAY;IAAW;IAAU;IAAa;IAAY;CAC7F", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/lib/database.ts"], "sourcesContent": ["import Database from 'better-sqlite3';\nimport { join } from 'path';\nimport bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\n\n// Re-export types from types file\nexport type { User, Mission, ClickLog, UserStats, HKDistrict } from './types';\nexport { HK_DISTRICTS } from './types';\n\n// Database instance\nlet db: Database.Database | null = null;\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';\n\nexport function getDatabase(): Database.Database {\n  if (!db) {\n    const dbPath = join(process.cwd(), 'wooden-fish.db');\n    db = new Database(dbPath);\n    \n    // Enable foreign keys\n    db.pragma('foreign_keys = ON');\n    \n    // Initialize database schema\n    initializeDatabase(db);\n  }\n  return db;\n}\n\nfunction initializeDatabase(database: Database.Database) {\n  // Create users table\n  database.exec(`\n    CREATE TABLE IF NOT EXISTS users (\n      id TEXT PRIMARY KEY,\n      username TEXT UNIQUE NOT NULL,\n      email TEXT UNIQUE NOT NULL,\n      password_hash TEXT NOT NULL,\n      display_name TEXT,\n      clan_district TEXT NOT NULL CHECK (clan_district IN (${HK_DISTRICTS.map(d => `'${d}'`).join(', ')})),\n      total_score INTEGER DEFAULT 0,\n      missions_completed INTEGER DEFAULT 0,\n      login_streak INTEGER DEFAULT 0,\n      last_login_date TEXT,\n      created_at TEXT DEFAULT (datetime('now')),\n      updated_at TEXT DEFAULT (datetime('now'))\n    )\n  `);\n\n  // Create user_stats table\n  database.exec(`\n    CREATE TABLE IF NOT EXISTS user_stats (\n      id TEXT PRIMARY KEY,\n      user_id TEXT NOT NULL,\n      date TEXT DEFAULT (date('now')),\n      daily_score INTEGER DEFAULT 0,\n      daily_missions_completed INTEGER DEFAULT 0,\n      daily_clicks INTEGER DEFAULT 0,\n      created_at TEXT DEFAULT (datetime('now')),\n      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,\n      UNIQUE(user_id, date)\n    )\n  `);\n\n  // Create missions table\n  database.exec(`\n    CREATE TABLE IF NOT EXISTS missions (\n      id TEXT PRIMARY KEY,\n      user_id TEXT NOT NULL,\n      mission_type TEXT DEFAULT 'hourly',\n      target_clicks INTEGER NOT NULL,\n      completed_clicks INTEGER DEFAULT 0,\n      status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'failed')),\n      score_awarded INTEGER DEFAULT 0,\n      created_at TEXT DEFAULT (datetime('now')),\n      expires_at TEXT NOT NULL,\n      updated_at TEXT DEFAULT (datetime('now')),\n      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE\n    )\n  `);\n\n  // Create click_logs table\n  database.exec(`\n    CREATE TABLE IF NOT EXISTS click_logs (\n      id TEXT PRIMARY KEY,\n      user_id TEXT NOT NULL,\n      mission_id TEXT NOT NULL,\n      click_x REAL,\n      click_y REAL,\n      click_timestamp TEXT,\n      server_timestamp TEXT DEFAULT (datetime('now')),\n      created_at TEXT DEFAULT (datetime('now')),\n      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,\n      FOREIGN KEY (mission_id) REFERENCES missions (id) ON DELETE CASCADE\n    )\n  `);\n\n  // Create indexes for better performance\n  database.exec(`\n    CREATE INDEX IF NOT EXISTS idx_missions_user_id ON missions (user_id);\n    CREATE INDEX IF NOT EXISTS idx_missions_status ON missions (status);\n    CREATE INDEX IF NOT EXISTS idx_click_logs_user_id ON click_logs (user_id);\n    CREATE INDEX IF NOT EXISTS idx_click_logs_mission_id ON click_logs (mission_id);\n    CREATE INDEX IF NOT EXISTS idx_user_stats_user_date ON user_stats (user_id, date);\n  `);\n\n  // Create trigger to update updated_at timestamp\n  database.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_users_updated_at\n    AFTER UPDATE ON users\n    BEGIN\n      UPDATE users SET updated_at = datetime('now') WHERE id = NEW.id;\n    END;\n  `);\n\n  database.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_missions_updated_at\n    AFTER UPDATE ON missions\n    BEGIN\n      UPDATE missions SET updated_at = datetime('now') WHERE id = NEW.id;\n    END;\n  `);\n}\n\n// Authentication functions\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12);\n}\n\nexport async function verifyPassword(password: string, hash: string): Promise<boolean> {\n  return bcrypt.compare(password, hash);\n}\n\nexport function generateToken(userId: string): string {\n  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' });\n}\n\nexport function verifyToken(token: string): { userId: string } | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as { userId: string };\n  } catch {\n    return null;\n  }\n}\n\n// User functions\nexport function createUser(userData: {\n  username: string;\n  email: string;\n  password_hash: string;\n  display_name: string;\n  clan_district: HKDistrict;\n}): User {\n  const db = getDatabase();\n  const id = crypto.randomUUID();\n  \n  const stmt = db.prepare(`\n    INSERT INTO users (id, username, email, password_hash, display_name, clan_district)\n    VALUES (?, ?, ?, ?, ?, ?)\n  `);\n  \n  stmt.run(id, userData.username, userData.email, userData.password_hash, userData.display_name, userData.clan_district);\n  \n  return getUserById(id)!;\n}\n\nexport function getUserById(id: string): User | null {\n  const db = getDatabase();\n  const stmt = db.prepare('SELECT * FROM users WHERE id = ?');\n  return stmt.get(id) as User | null;\n}\n\nexport function getUserByEmail(email: string): User | null {\n  const db = getDatabase();\n  const stmt = db.prepare('SELECT * FROM users WHERE email = ?');\n  return stmt.get(email) as User | null;\n}\n\nexport function getUserByUsername(username: string): User | null {\n  const db = getDatabase();\n  const stmt = db.prepare('SELECT * FROM users WHERE username = ?');\n  return stmt.get(username) as User | null;\n}\n\n// Mission functions\nexport function createMission(missionData: {\n  user_id: string;\n  target_clicks: number;\n  expires_at: string;\n}): Mission {\n  const db = getDatabase();\n  const id = crypto.randomUUID();\n  \n  const stmt = db.prepare(`\n    INSERT INTO missions (id, user_id, target_clicks, expires_at)\n    VALUES (?, ?, ?, ?)\n  `);\n  \n  stmt.run(id, missionData.user_id, missionData.target_clicks, missionData.expires_at);\n  \n  return getMissionById(id)!;\n}\n\nexport function getMissionById(id: string): Mission | null {\n  const db = getDatabase();\n  const stmt = db.prepare('SELECT * FROM missions WHERE id = ?');\n  return stmt.get(id) as Mission | null;\n}\n\nexport function getUserMissions(userId: string): Mission[] {\n  const db = getDatabase();\n  const stmt = db.prepare('SELECT * FROM missions WHERE user_id = ? ORDER BY created_at DESC');\n  return stmt.all(userId) as Mission[];\n}\n\nexport function updateMission(id: string, updates: Partial<Mission>): void {\n  const db = getDatabase();\n  const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');\n  const values = Object.values(updates);\n  \n  const stmt = db.prepare(`UPDATE missions SET ${fields} WHERE id = ?`);\n  stmt.run(...values, id);\n}\n\n// Click log functions\nexport function createClickLog(clickData: {\n  user_id: string;\n  mission_id: string;\n  click_x: number;\n  click_y: number;\n  click_timestamp: string;\n}): ClickLog {\n  const db = getDatabase();\n  const id = crypto.randomUUID();\n  \n  const stmt = db.prepare(`\n    INSERT INTO click_logs (id, user_id, mission_id, click_x, click_y, click_timestamp)\n    VALUES (?, ?, ?, ?, ?, ?)\n  `);\n  \n  stmt.run(id, clickData.user_id, clickData.mission_id, clickData.click_x, clickData.click_y, clickData.click_timestamp);\n  \n  const getStmt = db.prepare('SELECT * FROM click_logs WHERE id = ?');\n  return getStmt.get(id) as ClickLog;\n}\n\n// User stats functions\nexport function updateUserStats(userId: string, scoreChange: number = 0, missionCompleted: number = 0): void {\n  const db = getDatabase();\n  \n  // Update user totals\n  const updateUserStmt = db.prepare(`\n    UPDATE users \n    SET total_score = total_score + ?, missions_completed = missions_completed + ?\n    WHERE id = ?\n  `);\n  updateUserStmt.run(scoreChange, missionCompleted, userId);\n  \n  // Update daily stats\n  const today = new Date().toISOString().split('T')[0];\n  const upsertStatsStmt = db.prepare(`\n    INSERT INTO user_stats (id, user_id, date, daily_score, daily_missions_completed, daily_clicks)\n    VALUES (?, ?, ?, ?, ?, 1)\n    ON CONFLICT(user_id, date) DO UPDATE SET\n      daily_score = daily_score + ?,\n      daily_missions_completed = daily_missions_completed + ?,\n      daily_clicks = daily_clicks + 1\n  `);\n  \n  const statsId = crypto.randomUUID();\n  upsertStatsStmt.run(statsId, userId, today, scoreChange, missionCompleted, scoreChange, missionCompleted);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AAIA;;;;;;AAEA,oBAAoB;AACpB,IAAI,KAA+B;AAEnC,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAEtC,SAAS;IACd,IAAI,CAAC,IAAI;QACP,MAAM,SAAS,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI;QACnC,KAAK,IAAI,2HAAA,CAAA,UAAQ,CAAC;QAElB,sBAAsB;QACtB,GAAG,MAAM,CAAC;QAEV,6BAA6B;QAC7B,mBAAmB;IACrB;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,QAA2B;IACrD,qBAAqB;IACrB,SAAS,IAAI,CAAC,CAAC;;;;;;;2DAO0C,EAAE,aAAa,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM;;;;;;;;EAQtG,CAAC;IAED,0BAA0B;IAC1B,SAAS,IAAI,CAAC,CAAC;;;;;;;;;;;;EAYf,CAAC;IAED,wBAAwB;IACxB,SAAS,IAAI,CAAC,CAAC;;;;;;;;;;;;;;EAcf,CAAC;IAED,0BAA0B;IAC1B,SAAS,IAAI,CAAC,CAAC;;;;;;;;;;;;;EAaf,CAAC;IAED,wCAAwC;IACxC,SAAS,IAAI,CAAC,CAAC;;;;;;EAMf,CAAC;IAED,gDAAgD;IAChD,SAAS,IAAI,CAAC,CAAC;;;;;;EAMf,CAAC;IAED,SAAS,IAAI,CAAC,CAAC;;;;;;EAMf,CAAC;AACH;AAGO,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,IAAY;IACjE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC;QAAE;IAAO,GAAG,YAAY;QAAE,WAAW;IAAK;AAC5D;AAEO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,WAAW,QAM1B;IACC,MAAM,KAAK;IACX,MAAM,KAAK,OAAO,UAAU;IAE5B,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC;;;EAGzB,CAAC;IAED,KAAK,GAAG,CAAC,IAAI,SAAS,QAAQ,EAAE,SAAS,KAAK,EAAE,SAAS,aAAa,EAAE,SAAS,YAAY,EAAE,SAAS,aAAa;IAErH,OAAO,YAAY;AACrB;AAEO,SAAS,YAAY,EAAU;IACpC,MAAM,KAAK;IACX,MAAM,OAAO,GAAG,OAAO,CAAC;IACxB,OAAO,KAAK,GAAG,CAAC;AAClB;AAEO,SAAS,eAAe,KAAa;IAC1C,MAAM,KAAK;IACX,MAAM,OAAO,GAAG,OAAO,CAAC;IACxB,OAAO,KAAK,GAAG,CAAC;AAClB;AAEO,SAAS,kBAAkB,QAAgB;IAChD,MAAM,KAAK;IACX,MAAM,OAAO,GAAG,OAAO,CAAC;IACxB,OAAO,KAAK,GAAG,CAAC;AAClB;AAGO,SAAS,cAAc,WAI7B;IACC,MAAM,KAAK;IACX,MAAM,KAAK,OAAO,UAAU;IAE5B,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC;;;EAGzB,CAAC;IAED,KAAK,GAAG,CAAC,IAAI,YAAY,OAAO,EAAE,YAAY,aAAa,EAAE,YAAY,UAAU;IAEnF,OAAO,eAAe;AACxB;AAEO,SAAS,eAAe,EAAU;IACvC,MAAM,KAAK;IACX,MAAM,OAAO,GAAG,OAAO,CAAC;IACxB,OAAO,KAAK,GAAG,CAAC;AAClB;AAEO,SAAS,gBAAgB,MAAc;IAC5C,MAAM,KAAK;IACX,MAAM,OAAO,GAAG,OAAO,CAAC;IACxB,OAAO,KAAK,GAAG,CAAC;AAClB;AAEO,SAAS,cAAc,EAAU,EAAE,OAAyB;IACjE,MAAM,KAAK;IACX,MAAM,SAAS,OAAO,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC;IAClE,MAAM,SAAS,OAAO,MAAM,CAAC;IAE7B,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,oBAAoB,EAAE,OAAO,aAAa,CAAC;IACpE,KAAK,GAAG,IAAI,QAAQ;AACtB;AAGO,SAAS,eAAe,SAM9B;IACC,MAAM,KAAK;IACX,MAAM,KAAK,OAAO,UAAU;IAE5B,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC;;;EAGzB,CAAC;IAED,KAAK,GAAG,CAAC,IAAI,UAAU,OAAO,EAAE,UAAU,UAAU,EAAE,UAAU,OAAO,EAAE,UAAU,OAAO,EAAE,UAAU,eAAe;IAErH,MAAM,UAAU,GAAG,OAAO,CAAC;IAC3B,OAAO,QAAQ,GAAG,CAAC;AACrB;AAGO,SAAS,gBAAgB,MAAc,EAAE,cAAsB,CAAC,EAAE,mBAA2B,CAAC;IACnG,MAAM,KAAK;IAEX,qBAAqB;IACrB,MAAM,iBAAiB,GAAG,OAAO,CAAC,CAAC;;;;EAInC,CAAC;IACD,eAAe,GAAG,CAAC,aAAa,kBAAkB;IAElD,qBAAqB;IACrB,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACpD,MAAM,kBAAkB,GAAG,OAAO,CAAC,CAAC;;;;;;;EAOpC,CAAC;IAED,MAAM,UAAU,OAAO,UAAU;IACjC,gBAAgB,GAAG,CAAC,SAAS,QAAQ,OAAO,aAAa,kBAAkB,aAAa;AAC1F", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/lib/auth.ts"], "sourcesContent": ["import { cookies } from 'next/headers';\nimport { NextRequest } from 'next/server';\nimport { verifyToken, getUserById, type User } from './database';\n\nconst AUTH_COOKIE_NAME = 'auth-token';\n\nexport async function getUser(): Promise<User | null> {\n  try {\n    const cookieStore = await cookies();\n    const token = cookieStore.get(AUTH_COOKIE_NAME)?.value;\n    \n    if (!token) {\n      return null;\n    }\n    \n    const payload = verifyToken(token);\n    if (!payload) {\n      return null;\n    }\n    \n    return getUserById(payload.userId);\n  } catch (error) {\n    console.error('Error getting user:', error);\n    return null;\n  }\n}\n\nexport async function getUserFromRequest(request: NextRequest): Promise<User | null> {\n  try {\n    const token = request.cookies.get(AUTH_COOKIE_NAME)?.value;\n    \n    if (!token) {\n      return null;\n    }\n    \n    const payload = verifyToken(token);\n    if (!payload) {\n      return null;\n    }\n    \n    return getUserById(payload.userId);\n  } catch (error) {\n    console.error('Error getting user from request:', error);\n    return null;\n  }\n}\n\nexport function setAuthCookie(token: string): string {\n  return `${AUTH_COOKIE_NAME}=${token}; HttpOnly; Path=/; Max-Age=${7 * 24 * 60 * 60}; SameSite=Lax`;\n}\n\nexport function clearAuthCookie(): string {\n  return `${AUTH_COOKIE_NAME}=; HttpOnly; Path=/; Max-Age=0; SameSite=Lax`;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAEA;AAAA;;;AAEA,MAAM,mBAAmB;AAElB,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAChC,MAAM,QAAQ,YAAY,GAAG,CAAC,mBAAmB;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,OAAO,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,MAAM;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;IACT;AACF;AAEO,eAAe,mBAAmB,OAAoB;IAC3D,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAErD,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,OAAO,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,MAAM;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,OAAO,GAAG,iBAAiB,CAAC,EAAE,MAAM,4BAA4B,EAAE,IAAI,KAAK,KAAK,GAAG,cAAc,CAAC;AACpG;AAEO,SAAS;IACd,OAAO,GAAG,iBAAiB,4CAA4C,CAAC;AAC1E", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/app/api/auth/me/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getUserFromRequest } from '@/lib/auth';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const user = await getUserFromRequest(request);\n    \n    if (!user) {\n      return NextResponse.json(\n        { error: 'Not authenticated' },\n        { status: 401 }\n      );\n    }\n\n    return NextResponse.json({\n      user: {\n        id: user.id,\n        username: user.username,\n        email: user.email,\n        display_name: user.display_name,\n        clan_district: user.clan_district,\n        total_score: user.total_score,\n        missions_completed: user.missions_completed,\n        login_streak: user.login_streak,\n      },\n    });\n  } catch (error) {\n    console.error('Get user error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;QAEtC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;gBACJ,IAAI,KAAK,EAAE;gBACX,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,cAAc,KAAK,YAAY;gBAC/B,eAAe,KAAK,aAAa;gBACjC,aAAa,KAAK,WAAW;gBAC7B,oBAAoB,KAAK,kBAAkB;gBAC3C,cAAc,KAAK,YAAY;YACjC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}