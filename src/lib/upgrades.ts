// Cookie <PERSON>er-style upgrade system for Mokugyo Merit

export interface Upgrade {
  id: string
  name: string
  description: string
  icon: string
  baseCost: number
  costMultiplier: number
  effect: {
    type: 'click_power' | 'passive_generation' | 'mission_bonus' | 'special'
    value: number
  }
  category: 'tools' | 'incense' | 'meditation' | 'temple' | 'spiritual'
  unlockRequirement?: {
    type: 'total_merit' | 'upgrade_count' | 'achievement'
    value: number | string
  }
}

export interface UserUpgrade {
  upgrade_id: string
  level: number
  total_spent: number
}

// Buddhist-themed upgrades
export const UPGRADES: Upgrade[] = [
  // Tools Category - Clicking Power
  {
    id: 'wooden_mallet',
    name: 'Wooden Mallet',
    description: 'A simple wooden mallet increases your clicking power',
    icon: '🔨',
    baseCost: 15,
    costMultiplier: 1.15,
    effect: { type: 'click_power', value: 1 },
    category: 'tools'
  },
  {
    id: 'bamboo_striker',
    name: 'Bamboo Striker',
    description: 'Lightweight bamboo striker for faster clicking',
    icon: '🎋',
    baseCost: 100,
    costMultiplier: 1.15,
    effect: { type: 'click_power', value: 2 },
    category: 'tools',
    unlockRequirement: { type: 'total_merit', value: 500 }
  },
  {
    id: 'jade_mallet',
    name: 'Jade Mallet',
    description: 'Precious jade mallet blessed by monks',
    icon: '💎',
    baseCost: 1000,
    costMultiplier: 1.15,
    effect: { type: 'click_power', value: 5 },
    category: 'tools',
    unlockRequirement: { type: 'total_merit', value: 5000 }
  },

  // Incense Category - Passive Generation
  {
    id: 'sandalwood_incense',
    name: 'Sandalwood Incense',
    description: 'Fragrant incense generates merit over time',
    icon: '🕯️',
    baseCost: 50,
    costMultiplier: 1.2,
    effect: { type: 'passive_generation', value: 0.5 },
    category: 'incense'
  },
  {
    id: 'lotus_incense',
    name: 'Lotus Incense',
    description: 'Sacred lotus incense for continuous merit',
    icon: '🪷',
    baseCost: 500,
    costMultiplier: 1.2,
    effect: { type: 'passive_generation', value: 2 },
    category: 'incense',
    unlockRequirement: { type: 'total_merit', value: 2000 }
  },
  {
    id: 'dragon_incense',
    name: 'Dragon Incense',
    description: 'Mystical dragon incense with powerful aura',
    icon: '🐉',
    baseCost: 5000,
    costMultiplier: 1.2,
    effect: { type: 'passive_generation', value: 10 },
    category: 'incense',
    unlockRequirement: { type: 'total_merit', value: 25000 }
  },

  // Meditation Category - Mission Bonuses
  {
    id: 'meditation_cushion',
    name: 'Meditation Cushion',
    description: 'Comfortable cushion improves mission focus',
    icon: '🧘',
    baseCost: 200,
    costMultiplier: 1.25,
    effect: { type: 'mission_bonus', value: 0.1 },
    category: 'meditation'
  },
  {
    id: 'prayer_beads',
    name: 'Prayer Beads',
    description: 'Sacred beads enhance mission rewards',
    icon: '📿',
    baseCost: 1500,
    costMultiplier: 1.25,
    effect: { type: 'mission_bonus', value: 0.25 },
    category: 'meditation',
    unlockRequirement: { type: 'total_merit', value: 7500 }
  },

  // Temple Category - Major Upgrades
  {
    id: 'small_shrine',
    name: 'Small Shrine',
    description: 'A modest shrine that generates merit automatically',
    icon: '⛩️',
    baseCost: 10000,
    costMultiplier: 1.3,
    effect: { type: 'passive_generation', value: 50 },
    category: 'temple',
    unlockRequirement: { type: 'total_merit', value: 50000 }
  },
  {
    id: 'temple_bell',
    name: 'Temple Bell',
    description: 'Sacred bell that rings with accumulated merit',
    icon: '🔔',
    baseCost: 50000,
    costMultiplier: 1.3,
    effect: { type: 'passive_generation', value: 200 },
    category: 'temple',
    unlockRequirement: { type: 'total_merit', value: 250000 }
  },

  // Spiritual Category - Special Effects
  {
    id: 'enlightenment_scroll',
    name: 'Enlightenment Scroll',
    description: 'Ancient wisdom doubles all merit gains',
    icon: '📜',
    baseCost: 100000,
    costMultiplier: 1.5,
    effect: { type: 'special', value: 2 },
    category: 'spiritual',
    unlockRequirement: { type: 'total_merit', value: 500000 }
  }
]

// Calculate upgrade cost at specific level
export function getUpgradeCost(upgrade: Upgrade, currentLevel: number): number {
  return Math.floor(upgrade.baseCost * Math.pow(upgrade.costMultiplier, currentLevel))
}

// Calculate total effect from all upgrades
export function calculateTotalEffects(userUpgrades: UserUpgrade[]): {
  clickPower: number
  passiveGeneration: number
  missionBonus: number
  specialMultiplier: number
} {
  let clickPower = 1 // Base click power
  let passiveGeneration = 0
  let missionBonus = 0
  let specialMultiplier = 1

  for (const userUpgrade of userUpgrades) {
    const upgrade = UPGRADES.find(u => u.id === userUpgrade.upgrade_id)
    if (!upgrade || userUpgrade.level === 0) continue

    const totalEffect = upgrade.effect.value * userUpgrade.level

    switch (upgrade.effect.type) {
      case 'click_power':
        clickPower += totalEffect
        break
      case 'passive_generation':
        passiveGeneration += totalEffect
        break
      case 'mission_bonus':
        missionBonus += totalEffect
        break
      case 'special':
        specialMultiplier *= Math.pow(upgrade.effect.value, userUpgrade.level)
        break
    }
  }

  return {
    clickPower,
    passiveGeneration,
    missionBonus,
    specialMultiplier
  }
}

// Check if upgrade is unlocked for user
export function isUpgradeUnlocked(upgrade: Upgrade, totalMerit: number, upgradeCount: number): boolean {
  if (!upgrade.unlockRequirement) return true

  switch (upgrade.unlockRequirement.type) {
    case 'total_merit':
      return totalMerit >= (upgrade.unlockRequirement.value as number)
    case 'upgrade_count':
      return upgradeCount >= (upgrade.unlockRequirement.value as number)
    default:
      return false
  }
}

// Get upgrades by category
export function getUpgradesByCategory(category: string): Upgrade[] {
  return UPGRADES.filter(upgrade => upgrade.category === category)
}

// Format large numbers for display
export function formatNumber(num: number): string {
  if (num < 1000) return num.toString()
  if (num < 1000000) return (num / 1000).toFixed(1) + 'K'
  if (num < 1000000000) return (num / 1000000).toFixed(1) + 'M'
  if (num < 1000000000000) return (num / 1000000000).toFixed(1) + 'B'
  return (num / 1000000000000).toFixed(1) + 'T'
}
