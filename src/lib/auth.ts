import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';
import { verifyToken, getUserById, type User } from './database';

const AUTH_COOKIE_NAME = 'auth-token';

export async function getUser(): Promise<User | null> {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get(AUTH_COOKIE_NAME)?.value;
    
    if (!token) {
      return null;
    }
    
    const payload = verifyToken(token);
    if (!payload) {
      return null;
    }
    
    return getUserById(payload.userId);
  } catch (error) {
    console.error('Error getting user:', error);
    return null;
  }
}

export async function getUserFromRequest(request: NextRequest): Promise<User | null> {
  try {
    const token = request.cookies.get(AUTH_COOKIE_NAME)?.value;
    
    if (!token) {
      return null;
    }
    
    const payload = verifyToken(token);
    if (!payload) {
      return null;
    }
    
    return getUserById(payload.userId);
  } catch (error) {
    console.error('Error getting user from request:', error);
    return null;
  }
}

export function setAuthCookie(token: string): string {
  return `${AUTH_COOKIE_NAME}=${token}; HttpOnly; Path=/; Max-Age=${7 * 24 * 60 * 60}; SameSite=Lax`;
}

export function clearAuthCookie(): string {
  return `${AUTH_COOKIE_NAME}=; HttpOnly; Path=/; Max-Age=0; SameSite=Lax`;
}
