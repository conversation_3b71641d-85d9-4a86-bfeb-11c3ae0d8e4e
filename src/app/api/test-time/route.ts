import { NextResponse } from 'next/server'
import { getHKTime, isWithinActiveHours, getCurrentHourSlot, getNextMissionTime } from '@/lib/missionUtils'

export async function GET() {
  const hkTime = getHKTime()
  const isActive = isWithinActiveHours()
  const currentSlot = getCurrentHourSlot()
  const nextMissionTime = isActive ? null : getNextMissionTime()
  
  return NextResponse.json({
    currentTime: new Date().toISOString(),
    hkTime: hkTime.toISOString(),
    hkTimeFormatted: hkTime.toLocaleString('en-HK', { timeZone: 'Asia/Hong_Kong' }),
    currentHour: hkTime.getHours(),
    isWithinActiveHours: isActive,
    currentHourSlot: currentSlot,
    nextMissionTime: nextMissionTime?.toISOString(),
    activeHoursRange: '08:00-22:59 HK time'
  })
}
