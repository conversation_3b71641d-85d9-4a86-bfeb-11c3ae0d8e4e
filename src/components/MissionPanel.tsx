'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Clock, Target, CheckCircle, XCircle, AlertTriangle } from 'lucide-react'
import { Mission } from '@/lib/missionUtils'
import { formatTimeRemaining } from '@/lib/missionUtils'

interface MissionPanelProps {
  onMissionSelect: (mission: Mission | null) => void
}

interface MissionResponse {
  missions: Mission[]
  activeHours: boolean
  nextMissionTime?: string
  message?: string
  currentHourSlot?: string
}

export default function MissionPanel({ onMissionSelect }: MissionPanelProps) {
  const [missions, setMissions] = useState<Mission[]>([])
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeHours, setActiveHours] = useState(true)
  const [nextMissionTime, setNextMissionTime] = useState<string | null>(null)

  // Fetch missions from API
  const fetchMissions = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/missions')

      if (!response.ok) {
        throw new Error('Failed to fetch missions')
      }

      const data: MissionResponse = await response.json()

      setMissions(data.missions)
      setActiveHours(data.activeHours)
      setNextMissionTime(data.nextMissionTime || null)

      // Only auto-select if no mission is currently selected or if current mission is no longer active
      const activeMission = data.missions.find(m => m.status === 'active')
      const currentSelectedMission = data.missions.find(m => m.id === selectedMission?.id)

      // If current selected mission is still active, keep it selected
      if (currentSelectedMission && currentSelectedMission.status === 'active') {
        setSelectedMission(currentSelectedMission)
        onMissionSelect(currentSelectedMission)
      } else if (activeMission && !selectedMission) {
        // Only auto-select if no mission was previously selected
        setSelectedMission(activeMission)
        onMissionSelect(activeMission)
      } else if (!activeMission) {
        // No active missions available
        setSelectedMission(null)
        onMissionSelect(null)
      }

      setError(null)
    } catch (err) {
      console.error('Error fetching missions:', err)
      setError('Failed to load missions')
    } finally {
      setLoading(false)
    }
  }, [selectedMission, onMissionSelect])

  // Initial fetch and periodic updates
  useEffect(() => {
    fetchMissions()

    // Refresh missions every 60 seconds (reduced frequency to prevent constant refreshes)
    const interval = setInterval(fetchMissions, 60000)

    return () => clearInterval(interval)
  }, [fetchMissions])

  // Handle mission selection
  const handleMissionSelect = (mission: Mission) => {
    if (mission.status === 'active') {
      setSelectedMission(mission)
      onMissionSelect(mission)
    }
  }



  // Get status icon
  const getStatusIcon = (status: Mission['status']) => {
    switch (status) {
      case 'active':
        return <Target className="w-4 h-4 text-blue-500" />
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-500" />
    }
  }

  // Get status color
  const getStatusColor = (status: Mission['status']) => {
    switch (status) {
      case 'active':
        return 'border-blue-200 bg-blue-50'
      case 'completed':
        return 'border-green-200 bg-green-50'
      case 'failed':
        return 'border-red-200 bg-red-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
          <span className="ml-2 text-amber-700">Loading missions...</span>
        </div>
      </div>
    )
  }

  if (!activeHours) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
        <div className="text-center">
          <Clock className="w-12 h-12 text-amber-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-amber-800 mb-2">
            Missions Unavailable
          </h3>
          <p className="text-amber-700 mb-4">
            Missions are only available between 08:00-22:59 HK time
          </p>
          {nextMissionTime && (
            <p className="text-sm text-amber-600">
              Next missions available: {new Date(nextMissionTime).toLocaleString()}
            </p>
          )}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-red-200">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            Error Loading Missions
          </h3>
          <p className="text-red-700 mb-4">{error}</p>
          <button
            onClick={fetchMissions}
            className="px-4 py-2 bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
      <h3 className="text-lg font-semibold text-amber-800 mb-4 flex items-center">
        <Target className="w-5 h-5 mr-2" />
        Current Missions
      </h3>

      <div className="space-y-3">
        <AnimatePresence>
          {missions.map((mission, index) => (
            <motion.div
              key={mission.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all
                ${getStatusColor(mission.status)}
                ${selectedMission?.id === mission.id ? 'ring-2 ring-amber-400' : ''}
                ${mission.status === 'active' ? 'hover:shadow-md' : 'opacity-75'}
              `}
              onClick={() => handleMissionSelect(mission)}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  {getStatusIcon(mission.status)}
                  <span className="ml-2 font-medium text-gray-800">
                    Mission {index + 1}
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  {mission.status === 'active' && (
                    <span className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {formatTimeRemaining(mission.expires_at)}
                    </span>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm">
                  <span className="text-gray-600">Target: </span>
                  <span className="font-medium">{mission.target_clicks} clicks</span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-600">Progress: </span>
                  <span className="font-medium">
                    {mission.completed_clicks}/{mission.target_clicks}
                  </span>
                </div>
              </div>

              {mission.status === 'completed' && mission.score_awarded > 0 && (
                <div className="mt-2 text-sm text-green-600 font-medium">
                  +{mission.score_awarded} points earned
                </div>
              )}

              {mission.status === 'active' && (
                <div className="mt-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.min(100, (mission.completed_clicks / mission.target_clicks) * 100)}%`
                      }}
                    />
                  </div>
                </div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {selectedMission && selectedMission.status === 'active' && (
        <div className="mt-4 p-3 bg-amber-50 rounded-lg border border-amber-200">
          <p className="text-sm text-amber-800">
            <strong>Active Mission:</strong> Click the mokugyo {' '}
            {selectedMission.target_clicks - selectedMission.completed_clicks} more times
            {selectedMission.completed_clicks > selectedMission.target_clicks && (
              <span className="text-red-600 font-medium">
                {' '}(Warning: Exceeding target will result in -100 penalty!)
              </span>
            )}
          </p>
        </div>
      )}
    </div>
  )
}
