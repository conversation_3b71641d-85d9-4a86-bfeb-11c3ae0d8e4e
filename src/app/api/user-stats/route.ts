import { NextRequest, NextResponse } from 'next/server'
import { getUserFromRequest } from '@/lib/auth'
import { getDatabase } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const db = getDatabase()

    // Get user's basic stats
    const userStats = db.prepare(`
      SELECT 
        total_score,
        login_streak,
        created_at
      FROM users 
      WHERE id = ?
    `).get(user.id) as {
      total_score: number
      login_streak: number
      created_at: string
    } | undefined

    if (!userStats) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get total clicks from click_logs
    const clickStats = db.prepare(`
      SELECT COUNT(*) as total_clicks
      FROM click_logs 
      WHERE user_id = ?
    `).get(user.id) as { total_clicks: number } | undefined

    // Get missions completed
    const missionStats = db.prepare(`
      SELECT COUNT(*) as missions_completed
      FROM missions 
      WHERE status = 'completed'
    `).get() as { missions_completed: number } | undefined

    // Calculate approximate play time (based on click logs)
    const playTimeStats = db.prepare(`
      SELECT 
        MIN(created_at) as first_click,
        MAX(created_at) as last_click,
        COUNT(DISTINCT DATE(created_at)) as active_days
      FROM click_logs 
      WHERE user_id = ?
    `).get(user.id) as {
      first_click: string | null
      last_click: string | null
      active_days: number
    } | undefined

    // Calculate average clicks per minute (rough estimate)
    let averageClicksPerMinute = 0
    if (playTimeStats?.first_click && playTimeStats?.last_click && clickStats?.total_clicks) {
      const firstClick = new Date(playTimeStats.first_click)
      const lastClick = new Date(playTimeStats.last_click)
      const totalMinutes = Math.max(1, (lastClick.getTime() - firstClick.getTime()) / (1000 * 60))
      averageClicksPerMinute = Math.round(clickStats.total_clicks / totalMinutes)
    }

    // Get highest mission score (approximate based on total score growth)
    const highestMissionScore = Math.floor(userStats.total_score / Math.max(1, missionStats?.missions_completed || 1))

    // Estimate total play time (active days * average session time)
    const estimatedPlayTime = (playTimeStats?.active_days || 0) * 30 // Assume 30 minutes average per day

    const stats = {
      totalClicks: clickStats?.total_clicks || 0,
      missionsCompleted: missionStats?.missions_completed || 0,
      loginStreak: userStats.login_streak,
      totalPlayTime: estimatedPlayTime,
      averageClicksPerMinute: Math.max(0, averageClicksPerMinute),
      highestMissionScore: Math.max(0, highestMissionScore)
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('User stats API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user statistics' },
      { status: 500 }
    )
  }
}
