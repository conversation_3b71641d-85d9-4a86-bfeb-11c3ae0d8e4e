-- 木魚功德 (<PERSON><PERSON>) Database Schema
-- Copy and paste this entire file into your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Hong Kong Districts Enum
CREATE TYPE hk_district AS ENUM (
  'central_western', 'eastern', 'southern', 'wan_chai',
  'sham_shui_po', 'kowloon_city', 'kwun_tong', 'wong_tai_sin', 'yau_tsim_mong',
  'islands', 'kwai_tsing', 'north', 'sai_kung', 'sha_tin', 'tai_po', 'tsuen_wan', 'tuen_mun', 'yuen_long'
);

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  display_name VARCHAR(100),
  clan_district hk_district NOT NULL,
  total_score INTEGER DEFAULT 0,
  missions_completed INTEGER DEFAULT 0,
  login_streak INTEGER DEFAULT 0,
  last_login_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User stats table for detailed tracking
CREATE TABLE public.user_stats (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  date DATE DEFAULT CURRENT_DATE,
  daily_score INTEGER DEFAULT 0,
  daily_missions_completed INTEGER DEFAULT 0,
  daily_clicks INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Missions table
CREATE TABLE public.missions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  mission_type VARCHAR(20) DEFAULT 'hourly',
  target_clicks INTEGER NOT NULL,
  completed_clicks INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'failed')),
  score_awarded INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Click logs for anti-cheat analysis
CREATE TABLE public.click_logs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  mission_id UUID REFERENCES public.missions(id) ON DELETE CASCADE,
  click_x FLOAT,
  click_y FLOAT,
  click_timestamp TIMESTAMP WITH TIME ZONE,
  server_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function to update user stats
CREATE OR REPLACE FUNCTION update_user_stats(
  p_user_id UUID,
  p_score_change INTEGER DEFAULT 0,
  p_mission_completed INTEGER DEFAULT 0
)
RETURNS VOID AS $$
BEGIN
  -- Update user totals
  UPDATE public.users 
  SET 
    total_score = total_score + p_score_change,
    missions_completed = missions_completed + p_mission_completed,
    updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Update daily stats
  INSERT INTO public.user_stats (user_id, daily_score, daily_missions_completed, daily_clicks)
  VALUES (p_user_id, p_score_change, p_mission_completed, 1)
  ON CONFLICT (user_id, date)
  DO UPDATE SET
    daily_score = user_stats.daily_score + p_score_change,
    daily_missions_completed = user_stats.daily_missions_completed + p_mission_completed,
    daily_clicks = user_stats.daily_clicks + 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update login streak
CREATE OR REPLACE FUNCTION update_login_streak(p_user_id UUID)
RETURNS INTEGER AS $$
DECLARE
  current_streak INTEGER;
  last_login DATE;
BEGIN
  SELECT login_streak, last_login_date INTO current_streak, last_login
  FROM public.users WHERE id = p_user_id;
  
  -- If first login or login after gap, reset streak
  IF last_login IS NULL OR last_login < CURRENT_DATE - INTERVAL '1 day' THEN
    current_streak := 1;
  -- If consecutive day login, increment
  ELSIF last_login = CURRENT_DATE - INTERVAL '1 day' THEN
    current_streak := current_streak + 1;
  END IF;
  
  -- Update user record
  UPDATE public.users 
  SET 
    login_streak = current_streak,
    last_login_date = CURRENT_DATE,
    updated_at = NOW()
  WHERE id = p_user_id;
  
  RETURN current_streak;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_missions_updated_at BEFORE UPDATE ON public.missions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.missions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.click_logs ENABLE ROW LEVEL SECURITY;

-- Users can only see and edit their own data
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own stats" ON public.user_stats
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view own missions" ON public.missions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own missions" ON public.missions
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own missions" ON public.missions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own click logs" ON public.click_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own click logs" ON public.click_logs
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Allow reading leaderboards for all authenticated users
CREATE POLICY "Authenticated users can view leaderboards" ON public.users
  FOR SELECT USING (auth.role() = 'authenticated');

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, username, display_name, clan_district)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', 'user_' || substr(NEW.id::text, 1, 8)),
    COALESCE(NEW.raw_user_meta_data->>'display_name', 'New Player'),
    COALESCE((NEW.raw_user_meta_data->>'clan_district')::hk_district, 'central_western')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;
