'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Clock, Target, CheckCircle, XCircle, AlertTriangle, Trophy, Zap } from 'lucide-react'
import { Mission } from '@/lib/missionUtils'
import { formatTimeRemaining } from '@/lib/missionUtils'

interface MissionPanelProps {
  onMissionUpdate: (missions: Mission[]) => void
  currentClickCount: number
}

interface MissionResponse {
  missions: Mission[]
  activeHours: boolean
  nextMissionTime?: string
  message?: string
  currentHourSlot?: string
}

export default function MissionPanel({ onMissionUpdate, currentClickCount }: MissionPanelProps) {
  const [missions, setMissions] = useState<Mission[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeHours, setActiveHours] = useState(true)
  const [nextMissionTime, setNextMissionTime] = useState<string | null>(null)

  // Fetch missions from API
  const fetchMissions = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/missions')

      if (!response.ok) {
        throw new Error('Failed to fetch missions')
      }

      const data: MissionResponse = await response.json()

      const updatedMissions = data.missions || []
      setMissions(updatedMissions)
      setActiveHours(data.activeHours)
      setNextMissionTime(data.nextMissionTime || null)

      // Notify parent component of mission updates
      onMissionUpdate(updatedMissions)

      setError(null)
    } catch (err) {
      console.error('Error fetching missions:', err)
      setError('Failed to load missions')
    } finally {
      setLoading(false)
    }
  }, []) // Remove onMissionUpdate dependency to prevent infinite re-creation

  // Initial fetch and periodic updates
  useEffect(() => {
    fetchMissions()

    // Refresh missions every 30 seconds to check for new hourly missions
    const interval = setInterval(fetchMissions, 30000)

    return () => clearInterval(interval)
  }, [fetchMissions])

  // Note: Mission completion is handled by the main page click handler
  // This component only displays mission progress and status



  // Get status icon
  const getStatusIcon = (status: Mission['status']) => {
    switch (status) {
      case 'active':
        return <Target className="w-4 h-4 text-blue-500" />
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-500" />
    }
  }

  // Get status color
  const getStatusColor = (status: Mission['status']) => {
    switch (status) {
      case 'active':
        return 'border-blue-200 bg-blue-50'
      case 'completed':
        return 'border-green-200 bg-green-50'
      case 'failed':
        return 'border-red-200 bg-red-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
          <span className="ml-2 text-amber-700">Loading missions...</span>
        </div>
      </div>
    )
  }

  if (!activeHours) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
        <div className="text-center">
          <Clock className="w-12 h-12 text-amber-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-amber-800 mb-2">
            Missions Unavailable
          </h3>
          <p className="text-amber-700 mb-4">
            Missions are only available between 08:00-22:59 HK time
          </p>
          {nextMissionTime && (
            <p className="text-sm text-amber-600">
              Next missions available: {new Date(nextMissionTime).toLocaleString()}
            </p>
          )}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-red-200">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            Error Loading Missions
          </h3>
          <p className="text-red-700 mb-4">{error}</p>
          <button
            onClick={fetchMissions}
            className="px-4 py-2 bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
      <h3 className="text-lg font-semibold text-amber-800 mb-4 flex items-center">
        <Target className="w-5 h-5 mr-2" />
        Current Missions
      </h3>

      <div className="space-y-3">
        <AnimatePresence>
          {missions.map((mission, index) => {
            const progress = Math.min(100, (currentClickCount / mission.target_clicks) * 100)
            const isCompleted = mission.status === 'completed'
            const isFailed = mission.status === 'failed'
            const isActive = mission.status === 'active'

            return (
              <motion.div
                key={mission.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
                className={`
                  p-4 rounded-lg border-2 transition-all
                  ${getStatusColor(mission.status)}
                  ${isActive ? 'hover:shadow-md' : ''}
                `}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    {getStatusIcon(mission.status)}
                    <span className="ml-2 font-medium text-gray-800">
                      Mission {index + 1}
                    </span>
                    {isCompleted && (
                      <span className="ml-2 px-2 py-1 bg-green-500 text-white text-xs rounded-full font-medium flex items-center">
                        <Trophy className="w-3 h-3 mr-1" />
                        +{mission.score_awarded} Merit
                      </span>
                    )}
                    {isActive && progress >= 100 && (
                      <span className="ml-2 px-2 py-1 bg-blue-500 text-white text-xs rounded-full font-medium animate-pulse">
                        <Zap className="w-3 h-3 mr-1 inline" />
                        Ready!
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-600">
                    {isActive && (
                    <span className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {formatTimeRemaining(mission.expires_at)}
                    </span>
                  )}
                </div>
              </div>

                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm">
                    <span className="text-gray-600">Target: </span>
                    <span className="font-medium">{mission.target_clicks} clicks</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-600">Current: </span>
                    <span className="font-medium text-blue-600">
                      {Math.min(currentClickCount, mission.target_clicks)}/{mission.target_clicks}
                    </span>
                  </div>
                </div>

                {/* Real-time Progress Bar */}
                <div className="mb-2">
                  <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ${
                        progress >= 100
                          ? 'bg-gradient-to-r from-green-400 to-green-600'
                          : 'bg-gradient-to-r from-blue-400 to-blue-600'
                      }`}
                      style={{ width: `${Math.min(100, progress)}%` }}
                    />
                  </div>
                  <div className="text-xs text-gray-500 mt-1 text-center">
                    {progress.toFixed(1)}% Complete
                  </div>
                </div>

                {/* Mission Status Messages */}
                {isCompleted && (
                  <div className="text-sm text-green-600 font-medium flex items-center">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Mission Completed! +{mission.score_awarded} Merit earned
                  </div>
                )}

                {isFailed && (
                  <div className="text-sm text-red-600 font-medium flex items-center">
                    <XCircle className="w-4 h-4 mr-1" />
                    Mission Failed - Time expired
                  </div>
                )}

                {isActive && progress < 100 && (
                  <div className="text-sm text-blue-600">
                    Keep clicking! {mission.target_clicks - Math.min(currentClickCount, mission.target_clicks)} clicks remaining
                  </div>
                )}

                {isActive && progress >= 100 && (
                  <div className="text-sm text-green-600 font-medium animate-pulse">
                    🎉 Mission ready for completion! Click to finish!
                  </div>
                )}
              </motion.div>
            )
          })}
        </AnimatePresence>
      </div>

      {/* Mission System Explanation */}
      <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div className="text-sm text-blue-800">
          <strong>🎯 How Missions Work:</strong>
          <ul className="mt-2 space-y-1 text-xs">
            <li>• Missions automatically track your clicking progress</li>
            <li>• Complete missions by reaching the target click count</li>
            <li>• Earn Merit points when missions are completed</li>
            <li>• New missions generate every hour during active times (8AM-11PM HKT)</li>
          </ul>
          {nextMissionTime && activeHours && (
            <div className="mt-2 pt-2 border-t border-blue-200">
              <div className="text-xs text-blue-600">
                ⏰ Next hourly missions: {new Date(nextMissionTime).toLocaleString('en-US', {
                  timeZone: 'Asia/Hong_Kong',
                  hour: '2-digit',
                  minute: '2-digit',
                  month: 'short',
                  day: 'numeric'
                })} HKT
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
