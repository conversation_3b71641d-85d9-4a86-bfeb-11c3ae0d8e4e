'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import GameLayout from '@/components/GameLayout'
import Mokugyo from '@/components/Mokugyo'
import MissionPanel from '@/components/MissionPanel'
import Leaderboard from '@/components/Leaderboard'
import Upgrades from '@/components/Upgrades'
import PassiveIncome from '@/components/PassiveIncome'
import UserStats from '@/components/UserStats'
import { Mission } from '@/lib/missionUtils'

export default function Home() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [clickCount, setClickCount] = useState(0)
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [activeMissions, setActiveMissions] = useState<Mission[]>([])
  const [totalScore, setTotalScore] = useState(0)
  const [clickPower, setClickPower] = useState(1)
  const [passiveGeneration, setPassiveGeneration] = useState(0)
  const [activeTab, setActiveTab] = useState<'game' | 'upgrades'>('game')

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  // Load upgrade effects and passive earnings
  useEffect(() => {
    if (user) {
      loadUpgradeEffects()
    }
  }, [user])

  const loadUpgradeEffects = async () => {
    try {
      const response = await fetch('/api/upgrades')
      if (response.ok) {
        const data = await response.json()
        setClickPower(data.effects.clickPower || 1)
        setPassiveGeneration(data.effects.passiveGeneration || 0)
        setTotalScore(prev => prev + (data.passiveEarnings || 0))
      }
    } catch (error) {
      console.error('Error loading upgrade effects:', error)
    }
  }

  const handleMokugyoClick = async (clickData: { x: number; y: number; timestamp: number }) => {
    console.log('Mokugyo clicked:', clickData)

    // Enhanced click power from upgrades
    const actualClickPower = clickPower
    const newClickCount = clickCount + actualClickPower

    // Update click count immediately for responsive UI
    setClickCount(newClickCount)

    try {
      // Process click for any active missions
      const activeCurrentMissions = activeMissions.filter(m => m.status === 'active')

      if (activeCurrentMissions.length > 0) {
        // Send click to first active mission (missions auto-complete when target is reached)
        const primaryMission = activeCurrentMissions[0]

        const response = await fetch('/api/clicks', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            missionId: primaryMission.id,
            clickData,
            clickPower: actualClickPower
          })
        })

        if (response.ok) {
          const result = await response.json()
          setTotalScore(result.totalScore)
        }
      } else {
        // No active missions - still count clicks for merit
        const response = await fetch('/api/clicks', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            clickData,
            clickPower: actualClickPower
          })
        })

        if (response.ok) {
          const result = await response.json()
          setTotalScore(result.totalScore)
        }
      }
    } catch (error) {
      console.error('Error processing click:', error)
    }
  }

  const handleUpgradePurchase = async (upgradeId: string, cost: number): Promise<boolean> => {
    try {
      const response = await fetch('/api/upgrades', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ upgradeId })
      })

      if (response.ok) {
        const data = await response.json()
        setTotalScore(data.remainingMerit)
        setClickPower(data.effects.clickPower || 1)
        setPassiveGeneration(data.effects.passiveGeneration || 0)
        return true
      }
      return false
    } catch (error) {
      console.error('Error purchasing upgrade:', error)
      return false
    }
  }

  const handleClaimPassiveEarnings = async (): Promise<number> => {
    try {
      const response = await fetch('/api/upgrades')
      if (response.ok) {
        const data = await response.json()
        const earnings = data.passiveEarnings || 0
        setTotalScore(prev => prev + earnings)
        return earnings
      }
      return 0
    } catch (error) {
      console.error('Error claiming passive earnings:', error)
      return 0
    }
  }

  const handleMissionUpdate = (missions: Mission[]) => {
    setActiveMissions(missions)
    // Keep click count independent of missions for automatic progression
  }





  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🪵🐟</div>
          <div className="text-lg text-amber-800 font-medium">Loading...</div>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🪵🐟</div>
          <div className="text-lg text-amber-800 font-medium">Redirecting to login...</div>
        </div>
      </div>
    )
  }

  return (
    <GameLayout user={user}>
      <div className="flex-1 flex flex-col items-center justify-center p-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-amber-800 mb-2">
            木魚功德
          </h1>
          <p className="text-lg text-amber-600">
            Mokugyo Merit Game
          </p>
          <div className="mt-4 flex items-center justify-center space-x-4 text-sm">
            <div className="bg-white/80 px-3 py-1 rounded-full border border-amber-200">
              Merit: <span className="font-bold text-amber-800">{totalScore.toLocaleString()}</span>
            </div>
            <div className="bg-white/80 px-3 py-1 rounded-full border border-amber-200">
              Click Power: <span className="font-bold text-blue-600">{clickPower}</span>
            </div>
            {passiveGeneration > 0 && (
              <div className="bg-white/80 px-3 py-1 rounded-full border border-amber-200">
                Passive: <span className="font-bold text-green-600">{passiveGeneration.toFixed(1)}/sec</span>
              </div>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-amber-200 mb-8">
          <button
            onClick={() => setActiveTab('game')}
            className={`px-6 py-2 rounded-xl font-medium transition-colors ${
              activeTab === 'game'
                ? 'bg-amber-500 text-white shadow-md'
                : 'text-amber-700 hover:text-amber-800'
            }`}
          >
            🪵 Game
          </button>
          <button
            onClick={() => setActiveTab('upgrades')}
            className={`px-6 py-2 rounded-xl font-medium transition-colors ${
              activeTab === 'upgrades'
                ? 'bg-amber-500 text-white shadow-md'
                : 'text-amber-700 hover:text-amber-800'
            }`}
          >
            🛒 Upgrades
          </button>
        </div>

        {/* Game Tab Content */}
        {activeTab === 'game' && (
          <div className="w-full max-w-7xl">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Main Game Area */}
              <div className="lg:col-span-2 space-y-8">
                {/* Interactive Mokugyo */}
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-amber-200">
          <div className="flex justify-end mb-4">
            <button
              onClick={() => setSoundEnabled(!soundEnabled)}
              className={`
                px-3 py-1 rounded-full text-sm font-medium transition-colors
                ${soundEnabled
                  ? 'bg-amber-100 text-amber-800 hover:bg-amber-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }
              `}
            >
              {soundEnabled ? '🔊 Sound On' : '🔇 Sound Off'}
            </button>
          </div>

          <Mokugyo
            onMokugyoClick={handleMokugyoClick}
            clickCount={clickCount}
            soundEnabled={soundEnabled}
            disabled={false} // Always enabled - missions auto-track
          />
        </div>

        {/* Mission Panel */}
        <div className="mt-8 w-full max-w-4xl">
          <MissionPanel
            onMissionUpdate={handleMissionUpdate}
            currentClickCount={clickCount}
          />
        </div>

        {/* Merit Earning Status */}
        <div className="mt-6 text-center">
          <div className="text-sm text-blue-700 bg-blue-50 rounded-lg p-3 border border-blue-200">
            <strong>💰 Merit Earning:</strong> Each click earns you Merit points!
            <br />
            <span className="text-xs">
              Current Click Power: {clickPower} Merit per click
              {passiveGeneration > 0 && ` | Passive Generation: ${passiveGeneration} Merit/sec`}
            </span>
          </div>
        </div>

                {/* Leaderboard */}
                <div className="w-full">
                  <Leaderboard />
                </div>

                {/* Passive Income Display */}
                {passiveGeneration > 0 && (
                  <div className="w-full">
                    <PassiveIncome
                      passiveGeneration={passiveGeneration}
                      onClaimEarnings={handleClaimPassiveEarnings}
                    />
                  </div>
                )}
              </div>

              {/* Stats Sidebar */}
              <div className="lg:col-span-1">
                <UserStats
                  totalScore={totalScore}
                  clickPower={clickPower}
                  passiveGeneration={passiveGeneration}
                />
              </div>
            </div>
          </div>
        )}

        {/* Upgrades Tab Content */}
        {activeTab === 'upgrades' && (
          <div className="w-full max-w-6xl">
            <Upgrades
              userMerit={totalScore}
              onUpgradePurchase={handleUpgradePurchase}
            />
          </div>
        )}
      </div>
    </GameLayout>
  )
}
