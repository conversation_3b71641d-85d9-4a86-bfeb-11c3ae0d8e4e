{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/lib/types.ts"], "sourcesContent": ["// Hong Kong Districts\nexport const HK_DISTRICTS = [\n  'central_western', 'eastern', 'southern', 'wan_chai',\n  'sham_shui_po', 'kowloon_city', 'kwun_tong', 'wong_tai_sin', 'yau_tsim_mong',\n  'islands', 'kwai_tsing', 'north', 'sai_kung', 'sha_tin', 'tai_po', 'tsuen_wan', 'tuen_mun', 'yuen_long'\n] as const;\n\nexport type HKDistrict = typeof HK_DISTRICTS[number];\n\nexport interface User {\n  id: string;\n  username: string;\n  email: string;\n  password_hash: string;\n  display_name: string;\n  clan_district: HKDistrict;\n  total_score: number;\n  missions_completed: number;\n  login_streak: number;\n  last_login_date: string | null;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Mission {\n  id: string;\n  user_id: string;\n  mission_type: string;\n  target_clicks: number;\n  completed_clicks: number;\n  status: 'active' | 'completed' | 'failed';\n  score_awarded: number;\n  created_at: string;\n  expires_at: string;\n  updated_at: string;\n}\n\nexport interface ClickLog {\n  id: string;\n  user_id: string;\n  mission_id: string;\n  click_x: number;\n  click_y: number;\n  click_timestamp: string;\n  server_timestamp: string;\n  created_at: string;\n}\n\nexport interface UserStats {\n  id: string;\n  user_id: string;\n  date: string;\n  daily_score: number;\n  daily_missions_completed: number;\n  daily_clicks: number;\n  created_at: string;\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACf,MAAM,eAAe;IAC1B;IAAmB;IAAW;IAAY;IAC1C;IAAgB;IAAgB;IAAa;IAAgB;IAC7D;IAAW;IAAc;IAAS;IAAY;IAAW;IAAU;IAAa;IAAY;CAC7F", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/app/signup/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { HK_DISTRICTS } from '@/lib/types'\n\nexport default function SignupPage() {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [username, setUsername] = useState('')\n  const [displayName, setDisplayName] = useState('')\n  const [clanDistrict, setClanDistrict] = useState('')\n  const [error, setError] = useState('')\n  const [loading, setLoading] = useState(false)\n  const { signUp } = useAuth()\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setLoading(true)\n\n    if (!clanDistrict) {\n      setError('Please select a clan district')\n      setLoading(false)\n      return\n    }\n\n    const result = await signUp(email, password, username, displayName, clanDistrict)\n    \n    if (result.success) {\n      router.push('/')\n    } else {\n      setError(result.error || 'Signup failed')\n    }\n    \n    setLoading(false)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center p-4\">\n      <div className=\"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">木魚功德</h1>\n          <p className=\"text-gray-600\">Create your account to start earning merit</p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Email\n            </label>\n            <input\n              id=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Enter your email\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Username\n            </label>\n            <input\n              id=\"username\"\n              type=\"text\"\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n              required\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Choose a username\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"displayName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Display Name\n            </label>\n            <input\n              id=\"displayName\"\n              type=\"text\"\n              value={displayName}\n              onChange={(e) => setDisplayName(e.target.value)}\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Your display name (optional)\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"clanDistrict\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Clan District (Hong Kong)\n            </label>\n            <select\n              id=\"clanDistrict\"\n              value={clanDistrict}\n              onChange={(e) => setClanDistrict(e.target.value)}\n              required\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n            >\n              <option value=\"\">Select your district</option>\n              {HK_DISTRICTS.map((district) => (\n                <option key={district} value={district}>\n                  {district}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Password\n            </label>\n            <input\n              id=\"password\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Create a password\"\n            />\n          </div>\n\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\n              {error}\n            </div>\n          )}\n\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"w-full bg-amber-600 hover:bg-amber-700 disabled:bg-amber-400 text-white font-semibold py-3 px-4 rounded-lg transition-colors\"\n          >\n            {loading ? 'Creating account...' : 'Sign Up'}\n          </button>\n        </form>\n\n        <div className=\"mt-6 text-center\">\n          <p className=\"text-gray-600\">\n            Already have an account?{' '}\n            <Link href=\"/login\" className=\"text-amber-600 hover:text-amber-700 font-medium\">\n              Sign in\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QAEX,IAAI,CAAC,cAAc;YACjB,SAAS;YACT,WAAW;YACX;QACF;QAEA,MAAM,SAAS,MAAM,OAAO,OAAO,UAAU,UAAU,aAAa;QAEpE,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,SAAS,OAAO,KAAK,IAAI;QAC3B;QAEA,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAe,WAAU;8CAA+C;;;;;;8CAGvF,8OAAC;oCACC,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,QAAQ;oCACR,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,mHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,yBACjB,8OAAC;gDAAsB,OAAO;0DAC3B;+CADU;;;;;;;;;;;;;;;;;sCAOnB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;wBAIf,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,UAAU,wBAAwB;;;;;;;;;;;;8BAIvC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAgB;4BACF;0CACzB,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5F", "debugId": null}}]}