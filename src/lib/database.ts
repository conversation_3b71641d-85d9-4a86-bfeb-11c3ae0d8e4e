import Database from 'better-sqlite3';
import { join } from 'path';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// Re-export types from types file
export type { User, Mission, ClickLog, UserStats, HKDistrict } from './types';
export { HK_DISTRICTS } from './types';

// Database instance
let db: Database.Database | null = null;

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

export function getDatabase(): Database.Database {
  if (!db) {
    const dbPath = join(process.cwd(), 'wooden-fish.db');
    db = new Database(dbPath);
    
    // Enable foreign keys
    db.pragma('foreign_keys = ON');
    
    // Initialize database schema
    initializeDatabase(db);
  }
  return db;
}

function initializeDatabase(database: Database.Database) {
  // Create users table
  database.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      display_name TEXT,
      clan_district TEXT NOT NULL CHECK (clan_district IN (${HK_DISTRICTS.map(d => `'${d}'`).join(', ')})),
      total_score INTEGER DEFAULT 0,
      missions_completed INTEGER DEFAULT 0,
      login_streak INTEGER DEFAULT 0,
      last_login_date TEXT,
      created_at TEXT DEFAULT (datetime('now')),
      updated_at TEXT DEFAULT (datetime('now'))
    )
  `);

  // Create user_stats table
  database.exec(`
    CREATE TABLE IF NOT EXISTS user_stats (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      date TEXT DEFAULT (date('now')),
      daily_score INTEGER DEFAULT 0,
      daily_missions_completed INTEGER DEFAULT 0,
      daily_clicks INTEGER DEFAULT 0,
      created_at TEXT DEFAULT (datetime('now')),
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
      UNIQUE(user_id, date)
    )
  `);

  // Create missions table
  database.exec(`
    CREATE TABLE IF NOT EXISTS missions (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      mission_type TEXT DEFAULT 'hourly',
      target_clicks INTEGER NOT NULL,
      completed_clicks INTEGER DEFAULT 0,
      status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'failed')),
      score_awarded INTEGER DEFAULT 0,
      created_at TEXT DEFAULT (datetime('now')),
      expires_at TEXT NOT NULL,
      updated_at TEXT DEFAULT (datetime('now')),
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )
  `);

  // Create click_logs table
  database.exec(`
    CREATE TABLE IF NOT EXISTS click_logs (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      mission_id TEXT NOT NULL,
      click_x REAL,
      click_y REAL,
      click_timestamp TEXT,
      server_timestamp TEXT DEFAULT (datetime('now')),
      created_at TEXT DEFAULT (datetime('now')),
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
      FOREIGN KEY (mission_id) REFERENCES missions (id) ON DELETE CASCADE
    )
  `);

  // Create indexes for better performance
  database.exec(`
    CREATE INDEX IF NOT EXISTS idx_missions_user_id ON missions (user_id);
    CREATE INDEX IF NOT EXISTS idx_missions_status ON missions (status);
    CREATE INDEX IF NOT EXISTS idx_click_logs_user_id ON click_logs (user_id);
    CREATE INDEX IF NOT EXISTS idx_click_logs_mission_id ON click_logs (mission_id);
    CREATE INDEX IF NOT EXISTS idx_user_stats_user_date ON user_stats (user_id, date);
  `);

  // Create trigger to update updated_at timestamp
  database.exec(`
    CREATE TRIGGER IF NOT EXISTS update_users_updated_at
    AFTER UPDATE ON users
    BEGIN
      UPDATE users SET updated_at = datetime('now') WHERE id = NEW.id;
    END;
  `);

  database.exec(`
    CREATE TRIGGER IF NOT EXISTS update_missions_updated_at
    AFTER UPDATE ON missions
    BEGIN
      UPDATE missions SET updated_at = datetime('now') WHERE id = NEW.id;
    END;
  `);
}

// Authentication functions
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

export function generateToken(userId: string): string {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' });
}

export function verifyToken(token: string): { userId: string } | null {
  try {
    return jwt.verify(token, JWT_SECRET) as { userId: string };
  } catch {
    return null;
  }
}

// User functions
export function createUser(userData: {
  username: string;
  email: string;
  password_hash: string;
  display_name: string;
  clan_district: HKDistrict;
}): User {
  const db = getDatabase();
  const id = crypto.randomUUID();
  
  const stmt = db.prepare(`
    INSERT INTO users (id, username, email, password_hash, display_name, clan_district)
    VALUES (?, ?, ?, ?, ?, ?)
  `);
  
  stmt.run(id, userData.username, userData.email, userData.password_hash, userData.display_name, userData.clan_district);
  
  return getUserById(id)!;
}

export function getUserById(id: string): User | null {
  const db = getDatabase();
  const stmt = db.prepare('SELECT * FROM users WHERE id = ?');
  return stmt.get(id) as User | null;
}

export function getUserByEmail(email: string): User | null {
  const db = getDatabase();
  const stmt = db.prepare('SELECT * FROM users WHERE email = ?');
  return stmt.get(email) as User | null;
}

export function getUserByUsername(username: string): User | null {
  const db = getDatabase();
  const stmt = db.prepare('SELECT * FROM users WHERE username = ?');
  return stmt.get(username) as User | null;
}

// Mission functions
export function createMission(missionData: {
  user_id: string;
  target_clicks: number;
  expires_at: string;
}): Mission {
  const db = getDatabase();
  const id = crypto.randomUUID();
  
  const stmt = db.prepare(`
    INSERT INTO missions (id, user_id, target_clicks, expires_at)
    VALUES (?, ?, ?, ?)
  `);
  
  stmt.run(id, missionData.user_id, missionData.target_clicks, missionData.expires_at);
  
  return getMissionById(id)!;
}

export function getMissionById(id: string): Mission | null {
  const db = getDatabase();
  const stmt = db.prepare('SELECT * FROM missions WHERE id = ?');
  return stmt.get(id) as Mission | null;
}

export function getUserMissions(userId: string): Mission[] {
  const db = getDatabase();
  const stmt = db.prepare('SELECT * FROM missions WHERE user_id = ? ORDER BY created_at DESC');
  return stmt.all(userId) as Mission[];
}

export function updateMission(id: string, updates: Partial<Mission>): void {
  const db = getDatabase();
  const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
  const values = Object.values(updates);
  
  const stmt = db.prepare(`UPDATE missions SET ${fields} WHERE id = ?`);
  stmt.run(...values, id);
}

// Click log functions
export function createClickLog(clickData: {
  user_id: string;
  mission_id: string;
  click_x: number;
  click_y: number;
  click_timestamp: string;
}): ClickLog {
  const db = getDatabase();
  const id = crypto.randomUUID();
  
  const stmt = db.prepare(`
    INSERT INTO click_logs (id, user_id, mission_id, click_x, click_y, click_timestamp)
    VALUES (?, ?, ?, ?, ?, ?)
  `);
  
  stmt.run(id, clickData.user_id, clickData.mission_id, clickData.click_x, clickData.click_y, clickData.click_timestamp);
  
  const getStmt = db.prepare('SELECT * FROM click_logs WHERE id = ?');
  return getStmt.get(id) as ClickLog;
}

// User stats functions
export function updateUserStats(userId: string, scoreChange: number = 0, missionCompleted: number = 0): void {
  const db = getDatabase();
  
  // Update user totals
  const updateUserStmt = db.prepare(`
    UPDATE users 
    SET total_score = total_score + ?, missions_completed = missions_completed + ?
    WHERE id = ?
  `);
  updateUserStmt.run(scoreChange, missionCompleted, userId);
  
  // Update daily stats
  const today = new Date().toISOString().split('T')[0];
  const upsertStatsStmt = db.prepare(`
    INSERT INTO user_stats (id, user_id, date, daily_score, daily_missions_completed, daily_clicks)
    VALUES (?, ?, ?, ?, ?, 1)
    ON CONFLICT(user_id, date) DO UPDATE SET
      daily_score = daily_score + ?,
      daily_missions_completed = daily_missions_completed + ?,
      daily_clicks = daily_clicks + 1
  `);
  
  const statsId = crypto.randomUUID();
  upsertStatsStmt.run(statsId, userId, today, scoreChange, missionCompleted, scoreChange, missionCompleted);
}
