import { NextRequest, NextResponse } from 'next/server'
import { getUserFromRequest } from '@/lib/auth'
import { getMissionById, updateMission, createClickLog, updateUserStats, getDatabase } from '@/lib/database'
import { validateClick } from '@/lib/missionUtils'

interface ClickRequest {
  missionId: string
  clickData: {
    x: number
    y: number
    timestamp: number
  }
}

// POST /api/clicks - Process a click for a mission
export async function POST(request: NextRequest) {
  try {
    // Get user from auth
    const user = await getUserFromRequest(request)

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = user.id
    const body: ClickRequest = await request.json()

    if (!body.missionId || !body.clickData) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Get the mission
    const mission = getMissionById(body.missionId)

    if (!mission || mission.user_id !== userId) {
      return NextResponse.json({ error: 'Mission not found' }, { status: 404 })
    }

    // Anti-cheat: Check click timing (prevent too rapid clicks)
    const now = Date.now()
    const clickTime = body.clickData.timestamp
    const timeDiff = Math.abs(now - clickTime)
    
    if (timeDiff > 5000) { // Click timestamp too old (>5 seconds)
      return NextResponse.json({ error: 'Invalid click timestamp' }, { status: 400 })
    }

    // Log the click for anti-cheat analysis
    try {
      createClickLog({
        user_id: userId,
        mission_id: body.missionId,
        click_x: body.clickData.x,
        click_y: body.clickData.y,
        click_timestamp: new Date(clickTime).toISOString()
      })
    } catch (logError) {
      console.warn('Failed to log click:', logError)
      // Continue processing even if logging fails
    }

    // Update mission click count
    const newClickCount = mission.completed_clicks + 1

    // Validate the click
    const validation = validateClick(mission, newClickCount)

    if (!validation.isValid) {
      return NextResponse.json({
        error: 'Invalid click',
        mission: mission,
        validation
      }, { status: 400 })
    }

    // Update mission in database
    try {
      updateMission(body.missionId, {
        completed_clicks: newClickCount,
        status: validation.newStatus,
        score_awarded: validation.scoreAwarded
      })
    } catch (updateError) {
      console.error('Error updating mission:', updateError)
      return NextResponse.json({ error: 'Failed to update mission' }, { status: 500 })
    }

    // Get updated mission
    const updatedMission = getMissionById(body.missionId)

    // If mission completed or failed, update user stats
    if (validation.newStatus === 'completed' || validation.newStatus === 'failed') {
      const scoreChange = validation.scoreAwarded + validation.penalty

      // Update user stats
      try {
        updateUserStats(
          userId,
          scoreChange,
          validation.newStatus === 'completed' ? 1 : 0
        )
      } catch (statsError) {
        console.error('Error updating user stats:', statsError)
        // Don't fail the request if stats update fails
      }
    }

    return NextResponse.json({
      success: true,
      mission: updatedMission,
      validation: {
        isValid: validation.isValid,
        newStatus: validation.newStatus,
        penalty: validation.penalty,
        scoreAwarded: validation.scoreAwarded,
        clicksRemaining: Math.max(0, mission.target_clicks - newClickCount)
      }
    })

  } catch (error) {
    console.error('Click API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// GET /api/clicks - Get click statistics (for debugging)
export async function GET(request: NextRequest) {
  try {
    // Get user from auth
    const user = await getUserFromRequest(request)

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = user.id
    const url = new URL(request.url)
    const missionId = url.searchParams.get('missionId')

    if (!missionId) {
      return NextResponse.json({ error: 'Mission ID required' }, { status: 400 })
    }

    // Get click logs for the mission
    const db = getDatabase()
    const clickLogs = db.prepare(`
      SELECT * FROM click_logs
      WHERE user_id = ? AND mission_id = ?
      ORDER BY created_at DESC
    `).all(userId, missionId)

    return NextResponse.json({
      clickLogs: clickLogs || [],
      totalClicks: clickLogs?.length || 0
    })

  } catch (error) {
    console.error('Click GET API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
