'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
// Simple audio implementation without complex generation

interface ClickEffect {
  id: string
  x: number
  y: number
  timestamp: number
}

interface MokugyoProps {
  onMokugyoClick: (clickData: { x: number; y: number; timestamp: number }) => void
  disabled?: boolean
  clickCount?: number
  soundEnabled?: boolean
}

export default function Mokugyo({
  onMokugyoClick,
  disabled = false,
  clickCount = 0,
  soundEnabled = true
}: MokugyoProps) {
  const [clickEffects, setClickEffects] = useState<ClickEffect[]>([])
  const [isPressed, setIsPressed] = useState(false)
  const mokugyoRef = useRef<HTMLDivElement>(null)
  const clickIdRef = useRef(0)

  // Simple audio system using Web Audio API
  const audioContextRef = useRef<AudioContext | null>(null)

  // Initialize audio context
  useEffect(() => {
    if (soundEnabled && typeof window !== 'undefined') {
      try {
        audioContextRef.current = new (window.AudioContext || (window as unknown as { webkitAudioContext: typeof AudioContext }).webkitAudioContext)()
      } catch (error) {
        console.warn('Audio context not supported:', error)
      }
    }
  }, [soundEnabled])

  // Clean up click effects
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = Date.now()
      setClickEffects(prev => prev.filter(effect => now - effect.timestamp < 2000))
    }, 1000)

    return () => clearInterval(cleanup)
  }, [])

  // Play click sound using Web Audio API
  const playClickSound = useCallback(() => {
    if (!soundEnabled || !audioContextRef.current) return

    try {
      const ctx = audioContextRef.current
      
      // Resume context if suspended (required for some browsers)
      if (ctx.state === 'suspended') {
        ctx.resume()
      }

      // Create a simple wooden percussion sound
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()
      const filterNode = ctx.createBiquadFilter()

      // Connect nodes
      oscillator.connect(filterNode)
      filterNode.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Configure wooden percussion sound
      oscillator.type = 'triangle'
      oscillator.frequency.setValueAtTime(200, ctx.currentTime)
      oscillator.frequency.exponentialRampToValueAtTime(80, ctx.currentTime + 0.1)

      // Low-pass filter for wooden tone
      filterNode.type = 'lowpass'
      filterNode.frequency.setValueAtTime(800, ctx.currentTime)

      // Quick attack and decay for percussion
      gainNode.gain.setValueAtTime(0, ctx.currentTime)
      gainNode.gain.linearRampToValueAtTime(0.3, ctx.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.15)

      // Play the sound
      oscillator.start(ctx.currentTime)
      oscillator.stop(ctx.currentTime + 0.15)

    } catch (error) {
      console.warn('Error playing click sound:', error)
    }
  }, [soundEnabled])

  const handleClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (disabled) return

    const rect = mokugyoRef.current?.getBoundingClientRect()
    if (!rect) return

    const { clientX, clientY } = event

    // Calculate relative position within the mokugyo component
    const x = clientX - rect.left
    const y = clientY - rect.top

    const timestamp = Date.now()
    const clickId = `click-${++clickIdRef.current}-${timestamp}`

    // Add click effect
    setClickEffects(prev => [...prev, {
      id: clickId,
      x,
      y,
      timestamp
    }])

    // Play click sound
    playClickSound()

    // Call the parent callback
    onMokugyoClick({ x, y, timestamp })
  }, [disabled, onMokugyoClick, playClickSound])

  const handleMouseDown = useCallback(() => {
    if (!disabled) setIsPressed(true)
  }, [disabled])

  const handleMouseUp = useCallback(() => {
    setIsPressed(false)
  }, [])

  const handleTouchStart = useCallback((event: React.TouchEvent<HTMLDivElement>) => {
    if (disabled) return
    
    event.preventDefault() // Prevent scrolling
    setIsPressed(true)
  }, [disabled])

  const handleTouchEnd = useCallback((event: React.TouchEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsPressed(false)
    
    // Trigger click on touch end
    if (!disabled && event.changedTouches.length > 0) {
      const touch = event.changedTouches[0]
      const rect = mokugyoRef.current?.getBoundingClientRect()
      if (rect) {
        const x = touch.clientX - rect.left
        const y = touch.clientY - rect.top
        const timestamp = Date.now()
        
        setClickEffects(prev => [...prev, {
          id: `touch-${++clickIdRef.current}-${timestamp}`,
          x,
          y,
          timestamp
        }])
        
        playClickSound()
        onMokugyoClick({ x, y, timestamp })
      }
    }
  }, [disabled, onMokugyoClick, playClickSound])

  return (
    <div className="relative flex items-center justify-center">
      {/* Mokugyo Container */}
      <motion.div
        ref={mokugyoRef}
        className={`
          relative select-none cursor-pointer
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}
          transition-opacity duration-200
        `}
        animate={{
          scale: isPressed ? 0.95 : 1,
          rotate: isPressed ? -2 : 0
        }}
        transition={{
          type: "spring",
          stiffness: 400,
          damping: 25
        }}
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        style={{
          touchAction: 'manipulation' // Prevent double-tap zoom on mobile
        }}
      >
        {/* Mokugyo Image - Traditional Buddhist Wooden Fish Drum */}
        <div className="w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 mx-auto">
          <img
            src="https://artclasscurator.com/wp-content/uploads/2016/04/Muyu-Mogukyu-Buddhist-Wooden-Fish-Drum-The-Art-Curator-for-Kids2.jpg"
            alt="Mokugyo - Buddhist Wooden Fish Drum"
            className="w-full h-full object-contain rounded-lg shadow-lg"
            draggable={false}
          />
        </div>

        {/* Click Effects */}
        <AnimatePresence>
          {clickEffects.map((effect) => (
            <motion.div
              key={effect.id}
              className="absolute pointer-events-none text-2xl font-bold text-amber-600"
              style={{
                left: effect.x,
                top: effect.y,
                transform: 'translate(-50%, -50%)'
              }}
              initial={{ opacity: 1, scale: 0.5, y: 0 }}
              animate={{ 
                opacity: 0, 
                scale: 1.5, 
                y: -50,
                rotate: Math.random() * 40 - 20
              }}
              exit={{ opacity: 0 }}
              transition={{ 
                duration: 1.5,
                ease: "easeOut"
              }}
            >
              功德+1
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Click Counter */}
      <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="bg-amber-100 px-4 py-2 rounded-full border border-amber-300">
          <span className="text-sm font-medium text-amber-800">
            Clicks: {clickCount}
          </span>
        </div>
      </div>

      {/* Instructions */}
      <div className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 text-center">
        <p className="text-xs text-amber-600 max-w-xs">
          {disabled ? 'Select an active mission to start clicking' : 'Click or tap to gain merit'}
        </p>
      </div>
    </div>
  )
}
