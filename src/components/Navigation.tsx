'use client'

import { useState, useEffect } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { signOut } from '@/lib/supabase-utils'
import { LogOut, Trophy, User as UserIcon } from 'lucide-react'

interface NavigationProps {
  user: User | null
}

export default function Navigation({ user }: NavigationProps) {
  const [userProfile, setUserProfile] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (user) {
      fetchUserProfile()
    }
  }, [user])

  const fetchUserProfile = async () => {
    if (!user) return
    
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()
    
    if (data) {
      setUserProfile(data)
    }
  }

  const handleSignOut = async () => {
    setLoading(true)
    await signOut()
    setLoading(false)
  }

  return (
    <nav className="bg-white/80 backdrop-blur-sm border-b border-amber-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="text-2xl">🪵🐟</div>
            <div className="font-bold text-amber-800">
              <span className="hidden sm:inline">木魚功德</span>
              <span className="sm:hidden">木魚</span>
            </div>
          </div>

          {/* User Info */}
          {user && userProfile ? (
            <div className="flex items-center space-x-4">
              {/* Score Display */}
              <div className="hidden sm:flex items-center space-x-2 bg-amber-100 px-3 py-1 rounded-full">
                <Trophy className="w-4 h-4 text-amber-600" />
                <span className="text-sm font-medium text-amber-800">
                  {userProfile.total_score}
                </span>
              </div>

              {/* Clan Badge */}
              <div className="hidden md:flex items-center space-x-1 bg-blue-100 px-2 py-1 rounded-full">
                <span className="text-xs font-medium text-blue-800">
                  {userProfile.clan}
                </span>
              </div>

              {/* User Menu */}
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1">
                  <UserIcon className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700 hidden sm:inline">
                    {userProfile.username}
                  </span>
                </div>
                
                <button
                  onClick={handleSignOut}
                  disabled={loading}
                  className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors"
                  title="Sign Out"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            </div>
          ) : (
            <div className="text-sm text-gray-600">
              Welcome to 木魚功德
            </div>
          )}
        </div>
      </div>

      {/* Mobile Score Display */}
      {user && userProfile && (
        <div className="sm:hidden bg-amber-50 px-4 py-2 border-t border-amber-200">
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center space-x-2">
              <Trophy className="w-4 h-4 text-amber-600" />
              <span className="font-medium text-amber-800">
                Score: {userProfile.total_score}
              </span>
            </div>
            <div className="text-blue-800 font-medium">
              {userProfile.clan}
            </div>
          </div>
        </div>
      )}
    </nav>
  )
}
