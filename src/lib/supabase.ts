import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''

// Check if Supabase is configured
const isSupabaseConfigured = supabaseUrl && supabaseAnonKey &&
  supabaseUrl !== 'your_supabase_project_url' &&
  supabaseAnonKey !== 'your_supabase_anon_key'

// Client-side Supabase client
export const supabase = isSupabaseConfigured
  ? createBrowserClient(supabaseUrl, supabaseAnonKey)
  : null

// Server-side Supabase client (for API routes)
export const createServerClient = () => {
  return createClient(
    supabaseUrl,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )
}

// Database types
export interface User {
  id: string
  email: string
  username: string
  clan: string
  total_score: number
  login_streak: number
  missions_completed: number
  created_at: string
  last_login: string
}

export interface Mission {
  id: string
  user_id: string
  mission_type: 'hourly'
  target_clicks: number
  completed_clicks: number
  status: 'active' | 'completed' | 'failed'
  created_at: string
  expires_at: string
  score_awarded: number
}

export interface UserStats {
  user_id: string
  total_score: number
  missions_completed: number
  login_streak: number
  last_mission_completed: string
  updated_at: string
}

// Hong Kong Districts for clan selection
export const HK_DISTRICTS = [
  'Central and Western',
  'Eastern',
  'Southern', 
  'Wan Chai',
  'Sham Shui Po',
  'Kowloon City',
  'Kwun Tong',
  'Wong Tai Sin',
  'Yau Tsim Mong',
  'Islands',
  'Kwai Tsing',
  'North',
  'Sai Kung',
  'Sha Tin',
  'Tai Po',
  'Tsuen Wan',
  'Tuen Mun',
  'Yuen Long'
] as const

export type HKDistrict = typeof HK_DISTRICTS[number]
