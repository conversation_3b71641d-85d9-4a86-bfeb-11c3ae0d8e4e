// Hong Kong Districts
export const HK_DISTRICTS = [
  'central_western', 'eastern', 'southern', 'wan_chai',
  'sham_shui_po', 'kowloon_city', 'kwun_tong', 'wong_tai_sin', 'yau_tsim_mong',
  'islands', 'kwai_tsing', 'north', 'sai_kung', 'sha_tin', 'tai_po', 'tsuen_wan', 'tuen_mun', 'yuen_long'
] as const;

export type HKDistrict = typeof HK_DISTRICTS[number];

export interface User {
  id: string;
  username: string;
  email: string;
  password_hash: string;
  display_name: string;
  clan_district: HKDistrict;
  total_score: number;
  missions_completed: number;
  login_streak: number;
  last_login_date: string | null;
  created_at: string;
  updated_at: string;
}



export interface ClickLog {
  id: string;
  user_id: string;
  mission_id: string;
  click_x: number;
  click_y: number;
  click_timestamp: string;
  server_timestamp: string;
  created_at: string;
}

export interface UserStats {
  id: string;
  user_id: string;
  date: string;
  daily_score: number;
  daily_missions_completed: number;
  daily_clicks: number;
  created_at: string;
}
