import { NextRequest, NextResponse } from 'next/server';
import { createUser, getUserByE<PERSON>, getUserByUsername, hashPassword, generateToken, type HKDistrict } from '@/lib/database';
import { setAuthCookie } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const { email, password, username, displayName, clanDistrict } = await request.json();

    // Validate input
    if (!email || !password || !username || !clanDistrict) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUserByEmail = getUserByEmail(email);
    if (existingUserByEmail) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    const existingUserByUsername = getUserByUsername(username);
    if (existingUserByUsername) {
      return NextResponse.json(
        { error: 'Username already taken' },
        { status: 400 }
      );
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user
    const user = createUser({
      username,
      email,
      password_hash: passwordHash,
      display_name: displayName || username,
      clan_district: clanDistrict as HKDistrict,
    });

    // Generate token
    const token = generateToken(user.id);

    // Create response with auth cookie
    const response = NextResponse.json({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        display_name: user.display_name,
        clan_district: user.clan_district,
        total_score: user.total_score,
        missions_completed: user.missions_completed,
        login_streak: user.login_streak,
      },
    });

    response.headers.set('Set-Cookie', setAuthCookie(token));

    return response;
  } catch (error) {
    console.error('Signup error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
