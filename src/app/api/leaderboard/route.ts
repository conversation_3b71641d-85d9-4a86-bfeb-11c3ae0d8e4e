import { NextRequest, NextResponse } from 'next/server'
import { getUserFromRequest } from '@/lib/auth'
import { getDatabase } from '@/lib/database'

interface UserLeaderboardEntry {
  id: string
  username: string
  display_name: string
  clan_district: string
  total_score: number
  missions_completed: number
  rank: number
}

interface ClanLeaderboardEntry {
  clan_district: string
  total_score: number
  member_count: number
  avg_score: number
  rank: number
}

interface LeaderboardResponse {
  userLeaderboard: UserLeaderboardEntry[]
  clanLeaderboard: ClanLeaderboardEntry[]
  currentUser?: {
    rank: number
    total_score: number
    clan_district: string
  }
}

// GET /api/leaderboard - Get leaderboard data
export async function GET(request: NextRequest) {
  try {
    // Get user from auth (optional for viewing leaderboard)
    const user = await getUserFromRequest(request)
    const db = getDatabase()

    // Get top users leaderboard
    const userLeaderboardQuery = `
      SELECT 
        id,
        username,
        display_name,
        clan_district,
        total_score,
        missions_completed,
        ROW_NUMBER() OVER (ORDER BY total_score DESC, missions_completed DESC) as rank
      FROM users
      WHERE total_score > 0
      ORDER BY total_score DESC, missions_completed DESC
      LIMIT 50
    `
    
    const userLeaderboard = db.prepare(userLeaderboardQuery).all() as UserLeaderboardEntry[]

    // Get clan leaderboard (aggregate by district)
    const clanLeaderboardQuery = `
      SELECT 
        clan_district,
        SUM(total_score) as total_score,
        COUNT(*) as member_count,
        ROUND(AVG(total_score), 2) as avg_score,
        ROW_NUMBER() OVER (ORDER BY SUM(total_score) DESC, COUNT(*) DESC) as rank
      FROM users
      WHERE total_score > 0
      GROUP BY clan_district
      ORDER BY total_score DESC, member_count DESC
    `
    
    const clanLeaderboard = db.prepare(clanLeaderboardQuery).all() as ClanLeaderboardEntry[]

    // Get current user's rank if authenticated
    let currentUser = undefined
    if (user) {
      const currentUserRankQuery = `
        SELECT 
          total_score,
          clan_district,
          (
            SELECT COUNT(*) + 1
            FROM users u2
            WHERE u2.total_score > u1.total_score
            OR (u2.total_score = u1.total_score AND u2.missions_completed > u1.missions_completed)
          ) as rank
        FROM users u1
        WHERE u1.id = ?
      `
      
      const currentUserData = db.prepare(currentUserRankQuery).get(user.id) as {
        total_score: number
        clan_district: string
        rank: number
      } | undefined

      if (currentUserData) {
        currentUser = {
          rank: currentUserData.rank,
          total_score: currentUserData.total_score,
          clan_district: currentUserData.clan_district
        }
      }
    }

    const response: LeaderboardResponse = {
      userLeaderboard,
      clanLeaderboard,
      currentUser
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Leaderboard API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
