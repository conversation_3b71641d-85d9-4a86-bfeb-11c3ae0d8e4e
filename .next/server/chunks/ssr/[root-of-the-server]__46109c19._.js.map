{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/app/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function LoginPage() {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [error, setError] = useState('')\n  const [loading, setLoading] = useState(false)\n  const { signIn } = useAuth()\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setLoading(true)\n\n    const result = await signIn(email, password)\n    \n    if (result.success) {\n      router.push('/')\n    } else {\n      setError(result.error || 'Login failed')\n    }\n    \n    setLoading(false)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center p-4\">\n      <div className=\"bg-white rounded-2xl shadow-xl p-8 w-full max-w-md\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">木魚功德</h1>\n          <p className=\"text-gray-600\">Sign in to continue your merit journey</p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Email\n            </label>\n            <input\n              id=\"email\"\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Enter your email\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Password\n            </label>\n            <input\n              id=\"password\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n              placeholder=\"Enter your password\"\n            />\n          </div>\n\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\n              {error}\n            </div>\n          )}\n\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"w-full bg-amber-600 hover:bg-amber-700 disabled:bg-amber-400 text-white font-semibold py-3 px-4 rounded-lg transition-colors\"\n          >\n            {loading ? 'Signing in...' : 'Sign In'}\n          </button>\n        </form>\n\n        <div className=\"mt-6 text-center\">\n          <p className=\"text-gray-600\">\n            Don't have an account?{' '}\n            <Link href=\"/signup\" className=\"text-amber-600 hover:text-amber-700 font-medium\">\n              Sign up\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QAEX,MAAM,SAAS,MAAM,OAAO,OAAO;QAEnC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,SAAS,OAAO,KAAK,IAAI;QAC3B;QAEA,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,QAAQ;oCACR,WAAU;oCACV,aAAY;;;;;;;;;;;;wBAIf,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,UAAU,kBAAkB;;;;;;;;;;;;8BAIjC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAgB;4BACJ;0CACvB,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;0CAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7F", "debugId": null}}]}