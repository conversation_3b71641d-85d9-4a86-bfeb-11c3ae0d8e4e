'use client'

import { useCallback, useRef, useState, useEffect, useMemo } from 'react'

interface UseAudioOptions {
  volume?: number
  preload?: boolean
}

interface AudioState {
  isLoaded: boolean
  isPlaying: boolean
  error: string | null
  canPlay: boolean
}

export function useAudio(src: string, options: UseAudioOptions = {}) {
  const { volume = 0.5, preload = true } = options
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const [audioState, setAudioState] = useState<AudioState>({
    isLoaded: false,
    isPlaying: false,
    error: null,
    canPlay: false
  })

  // Initialize audio
  useEffect(() => {
    if (typeof window === 'undefined') return

    try {
      const audio = new Audio()
      audio.volume = volume
      audio.preload = preload ? 'auto' : 'none'
      
      // Event listeners
      const handleCanPlay = () => {
        setAudioState(prev => ({ ...prev, canPlay: true, isLoaded: true }))
      }

      const handleError = (e: Event) => {
        console.warn('Audio load error:', e)
        setAudioState(prev => ({ 
          ...prev, 
          error: 'Failed to load audio',
          canPlay: false 
        }))
      }

      const handlePlay = () => {
        setAudioState(prev => ({ ...prev, isPlaying: true }))
      }

      const handleEnded = () => {
        setAudioState(prev => ({ ...prev, isPlaying: false }))
      }

      const handlePause = () => {
        setAudioState(prev => ({ ...prev, isPlaying: false }))
      }

      audio.addEventListener('canplay', handleCanPlay)
      audio.addEventListener('error', handleError)
      audio.addEventListener('play', handlePlay)
      audio.addEventListener('ended', handleEnded)
      audio.addEventListener('pause', handlePause)

      // Set source and load
      audio.src = src
      if (preload) {
        audio.load()
      }

      audioRef.current = audio

      return () => {
        audio.removeEventListener('canplay', handleCanPlay)
        audio.removeEventListener('error', handleError)
        audio.removeEventListener('play', handlePlay)
        audio.removeEventListener('ended', handleEnded)
        audio.removeEventListener('pause', handlePause)
        audio.pause()
        audio.src = ''
      }
    } catch (error) {
      console.warn('Audio initialization error:', error)
      setAudioState(prev => ({ 
        ...prev, 
        error: 'Audio not supported',
        canPlay: false 
      }))
    }
  }, [src, volume, preload])

  const play = useCallback(async () => {
    if (!audioRef.current || !audioState.canPlay) {
      return false
    }

    try {
      // Reset to beginning
      audioRef.current.currentTime = 0
      await audioRef.current.play()
      return true
    } catch (error) {
      console.warn('Audio play error:', error)
      setAudioState(prev => ({ 
        ...prev, 
        error: 'Failed to play audio' 
      }))
      return false
    }
  }, [audioState.canPlay])

  const pause = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
    }
  }, [])

  const setVolume = useCallback((newVolume: number) => {
    if (audioRef.current) {
      audioRef.current.volume = Math.max(0, Math.min(1, newVolume))
    }
  }, [])

  return {
    play,
    pause,
    setVolume,
    ...audioState
  }
}

// Hook for multiple audio files
export function useMultipleAudio(audioFiles: Record<string, string>, options: UseAudioOptions = {}) {
  // Create audio hooks for each file using useMemo to prevent recreation
  const audioHooks = useMemo(() => {
    const hooks: Record<string, ReturnType<typeof useAudio>> = {}
    Object.entries(audioFiles).forEach(([key, src]) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      hooks[key] = useAudio(src, options)
    })
    return hooks
  }, [audioFiles, options])

  const playSound = useCallback((soundKey: string) => {
    const audioHook = audioHooks[soundKey]
    if (audioHook) {
      return audioHook.play()
    }
    return Promise.resolve(false)
  }, [audioHooks])

  const allLoaded = Object.values(audioHooks).every(hook => hook.isLoaded)
  const hasErrors = Object.values(audioHooks).some(hook => hook.error)

  return {
    playSound,
    audioHooks,
    allLoaded,
    hasErrors
  }
}
