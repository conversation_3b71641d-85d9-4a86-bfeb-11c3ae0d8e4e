'use client'

import { useState, useEffect } from 'react'
import { Clock, TrendingUp, Gift } from 'lucide-react'
import { formatNumber } from '@/lib/upgrades'

interface PassiveIncomeProps {
  passiveGeneration: number
  onClaimEarnings: () => Promise<number>
}

export default function PassiveIncome({ passiveGeneration, onClaimEarnings }: PassiveIncomeProps) {
  const [currentEarnings, setCurrentEarnings] = useState(0)
  const [claiming, setClaiming] = useState(false)
  const [lastClaimed, setLastClaimed] = useState<number>(0)

  useEffect(() => {
    if (passiveGeneration <= 0) return

    // Update earnings every second
    const interval = setInterval(() => {
      setCurrentEarnings(prev => prev + passiveGeneration)
    }, 1000)

    return () => clearInterval(interval)
  }, [passiveGeneration])

  const handleClaimEarnings = async () => {
    if (currentEarnings <= 0 || claiming) return

    setClaiming(true)
    try {
      const actualEarnings = await onClaimEarnings()
      setLastClaimed(actualEarnings)
      setCurrentEarnings(0)
      
      // Show claimed amount briefly
      setTimeout(() => setLastClaimed(0), 3000)
    } catch (error) {
      console.error('Error claiming earnings:', error)
    } finally {
      setClaiming(false)
    }
  }

  if (passiveGeneration <= 0) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
        <div className="text-center">
          <TrendingUp className="w-12 h-12 mx-auto mb-3 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">Passive Income</h3>
          <p className="text-sm text-gray-500">
            Purchase incense or temple upgrades to generate merit automatically!
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <TrendingUp className="w-5 h-5 text-green-500 mr-2" />
          Passive Income
        </h3>
        <div className="text-sm text-green-600 font-medium">
          +{formatNumber(passiveGeneration)}/sec
        </div>
      </div>

      {/* Current Earnings Display */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 mb-4 border border-green-200">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-sm text-green-700 mb-1">Accumulated Merit</div>
            <div className="text-2xl font-bold text-green-800">
              {formatNumber(Math.floor(currentEarnings))}
            </div>
          </div>
          <div className="text-right">
            <Clock className="w-8 h-8 text-green-500 mx-auto mb-1" />
            <div className="text-xs text-green-600">Auto-generating</div>
          </div>
        </div>
      </div>

      {/* Claim Button */}
      <button
        onClick={handleClaimEarnings}
        disabled={currentEarnings <= 0 || claiming}
        className={`w-full py-3 px-4 rounded-lg font-medium transition-all flex items-center justify-center space-x-2 ${
          currentEarnings > 0 && !claiming
            ? 'bg-green-500 text-white hover:bg-green-600 shadow-md hover:shadow-lg'
            : 'bg-gray-200 text-gray-500 cursor-not-allowed'
        }`}
      >
        <Gift className="w-5 h-5" />
        <span>
          {claiming 
            ? 'Claiming...' 
            : currentEarnings > 0 
            ? `Claim ${formatNumber(Math.floor(currentEarnings))} Merit`
            : 'No Merit to Claim'
          }
        </span>
      </button>

      {/* Last Claimed Notification */}
      {lastClaimed > 0 && (
        <div className="mt-3 p-2 bg-green-100 border border-green-300 rounded-lg text-center">
          <div className="text-sm text-green-800">
            ✨ Claimed {formatNumber(lastClaimed)} merit!
          </div>
        </div>
      )}

      {/* Generation Rate Info */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="text-xs text-gray-600 text-center">
          <div className="grid grid-cols-3 gap-2">
            <div>
              <div className="font-medium">{formatNumber(passiveGeneration * 60)}</div>
              <div>per minute</div>
            </div>
            <div>
              <div className="font-medium">{formatNumber(passiveGeneration * 3600)}</div>
              <div>per hour</div>
            </div>
            <div>
              <div className="font-medium">{formatNumber(passiveGeneration * 86400)}</div>
              <div>per day</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
