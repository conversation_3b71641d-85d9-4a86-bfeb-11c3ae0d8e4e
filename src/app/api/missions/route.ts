import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { 
  getMissionsForCurrentHour, 
  isWithinActiveHours, 
  getCurrentHourSlot,
  getNextMissionTime 
} from '@/lib/missionUtils'

// GET /api/missions - Get current missions for user
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient()
    
    // Get user from session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id

    // Check if we're within active hours
    if (!isWithinActiveHours()) {
      const nextMissionTime = getNextMissionTime()
      return NextResponse.json({
        missions: [],
        activeHours: false,
        nextMissionTime: nextMissionTime.toISOString(),
        message: 'Missions are only available between 08:00-22:59 HK time'
      })
    }

    const currentHourSlot = getCurrentHourSlot()

    // Get existing missions for current hour
    const { data: existingMissions, error: fetchError } = await supabase
      .from('missions')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', currentHourSlot)
      .lt('created_at', new Date(new Date(currentHourSlot).getTime() + 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: true })

    if (fetchError) {
      console.error('Error fetching missions:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch missions' }, { status: 500 })
    }

    // If we don't have 3 missions for this hour, generate them
    if (!existingMissions || existingMissions.length < 3) {
      const missionsToGenerate = getMissionsForCurrentHour(userId)
      const missionsToInsert = missionsToGenerate.slice(existingMissions?.length || 0)

      if (missionsToInsert.length > 0) {
        const { data: newMissions, error: insertError } = await supabase
          .from('missions')
          .insert(missionsToInsert)
          .select()

        if (insertError) {
          console.error('Error creating missions:', insertError)
          return NextResponse.json({ error: 'Failed to create missions' }, { status: 500 })
        }

        // Combine existing and new missions
        const allMissions = [...(existingMissions || []), ...(newMissions || [])]
        
        return NextResponse.json({
          missions: allMissions,
          activeHours: true,
          currentHourSlot,
          generated: missionsToInsert.length
        })
      }
    }

    return NextResponse.json({
      missions: existingMissions || [],
      activeHours: true,
      currentHourSlot
    })

  } catch (error) {
    console.error('Mission API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/missions - Create new missions (admin/debug only)
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient()
    
    // Get user from session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const body = await request.json()

    // Force generate missions (for testing)
    if (body.force === true) {
      const missionsToGenerate = getMissionsForCurrentHour(userId)
      
      const { data: newMissions, error: insertError } = await supabase
        .from('missions')
        .insert(missionsToGenerate)
        .select()

      if (insertError) {
        console.error('Error force creating missions:', insertError)
        return NextResponse.json({ error: 'Failed to create missions' }, { status: 500 })
      }

      return NextResponse.json({
        missions: newMissions,
        message: 'Missions force generated'
      })
    }

    return NextResponse.json({ error: 'Invalid request' }, { status: 400 })

  } catch (error) {
    console.error('Mission POST API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
