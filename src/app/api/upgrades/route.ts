import { NextRequest, NextResponse } from 'next/server'
import { getUserFromRequest } from '@/lib/auth'
import { getUserUpgrades, purchaseUpgrade, updateUserUpgradeEffects, claimPassiveEarnings } from '@/lib/database'
import { UPGRADES, getUpgradeCost, calculateTotalEffects } from '@/lib/upgrades'

// GET /api/upgrades - Get user's upgrades and passive earnings
export async function GET(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request)

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's upgrades
    const upgrades = getUserUpgrades(user.id)

    // Calculate and claim passive earnings
    const passiveEarnings = claimPassiveEarnings(user.id)

    // Calculate total effects from upgrades
    const effects = calculateTotalEffects(upgrades)

    // Update user's upgrade effects in database
    updateUserUpgradeEffects(user.id, effects)

    return NextResponse.json({
      upgrades,
      effects,
      passiveEarnings
    })

  } catch (error) {
    console.error('Upgrades GET API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/upgrades - Purchase an upgrade
export async function POST(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request)

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { upgradeId } = body

    if (!upgradeId) {
      return NextResponse.json({ error: 'Upgrade ID required' }, { status: 400 })
    }

    // Find the upgrade
    const upgrade = UPGRADES.find(u => u.id === upgradeId)
    if (!upgrade) {
      return NextResponse.json({ error: 'Upgrade not found' }, { status: 404 })
    }

    // Get current user upgrades to calculate cost
    const userUpgrades = getUserUpgrades(user.id)
    const currentUpgrade = userUpgrades.find(u => u.upgrade_id === upgradeId)
    const currentLevel = currentUpgrade?.level || 0
    const cost = getUpgradeCost(upgrade, currentLevel)

    // Check if user has enough merit
    if (user.total_score < cost) {
      return NextResponse.json({ 
        error: 'Insufficient merit',
        required: cost,
        current: user.total_score
      }, { status: 400 })
    }

    // Purchase the upgrade
    const success = purchaseUpgrade(user.id, upgradeId, cost)

    if (!success) {
      return NextResponse.json({ error: 'Failed to purchase upgrade' }, { status: 500 })
    }

    // Get updated upgrades and calculate new effects
    const updatedUpgrades = getUserUpgrades(user.id)
    const newEffects = calculateTotalEffects(updatedUpgrades)

    // Update user's upgrade effects
    updateUserUpgradeEffects(user.id, newEffects)

    return NextResponse.json({
      success: true,
      upgrade: {
        id: upgradeId,
        level: (currentUpgrade?.level || 0) + 1,
        cost
      },
      effects: newEffects,
      remainingMerit: user.total_score - cost
    })

  } catch (error) {
    console.error('Upgrades POST API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
