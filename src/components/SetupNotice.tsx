export default function SetupNotice() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="text-6xl mb-4">🪵🐟</div>
          <h1 className="text-3xl font-bold text-amber-800 mb-2">
            木魚功德
          </h1>
          <p className="text-amber-600">
            Traditional Merit Accumulation Game
          </p>
        </div>

        {/* Setup Instructions */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-amber-200">
          <h2 className="text-2xl font-bold text-amber-800 mb-6">
            🚀 Setup Required
          </h2>
          
          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-800 mb-2">
                1. Create a Supabase Project
              </h3>
              <p className="text-blue-700 text-sm">
                Go to <a href="https://supabase.com" target="_blank" rel="noopener noreferrer" className="underline">supabase.com</a> and create a new project.
              </p>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 mb-2">
                2. Set up Database Schema
              </h3>
              <p className="text-green-700 text-sm mb-2">
                Run the SQL commands from <code className="bg-green-100 px-1 rounded">supabase-schema.sql</code> in your Supabase SQL editor.
              </p>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h3 className="font-semibold text-purple-800 mb-2">
                3. Configure Environment Variables
              </h3>
              <p className="text-purple-700 text-sm mb-3">
                Update your <code className="bg-purple-100 px-1 rounded">.env.local</code> file with your Supabase credentials:
              </p>
              <div className="bg-gray-900 text-green-400 p-3 rounded text-xs font-mono">
                <div>NEXT_PUBLIC_SUPABASE_URL=your_project_url</div>
                <div>NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key</div>
                <div>SUPABASE_SERVICE_ROLE_KEY=your_service_role_key</div>
              </div>
            </div>

            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <h3 className="font-semibold text-amber-800 mb-2">
                4. Restart Development Server
              </h3>
              <p className="text-amber-700 text-sm">
                After updating the environment variables, restart your development server with <code className="bg-amber-100 px-1 rounded">npm run dev</code>.
              </p>
            </div>
          </div>

          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-semibold text-gray-800 mb-2">
              📋 Features Overview
            </h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Interactive 木魚 (wooden fish) clicking with animations</li>
              <li>• Time-based mission system (3 missions per hour, 08:00-22:59)</li>
              <li>• Anti-cheat protection with server-side validation</li>
              <li>• Hong Kong district-based clan system</li>
              <li>• Real-time leaderboards and scoring</li>
              <li>• User authentication and profile management</li>
              <li>• Login streak tracking and milestones</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
