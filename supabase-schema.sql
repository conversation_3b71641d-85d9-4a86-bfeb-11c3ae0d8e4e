-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- <PERSON><PERSON> custom types
CREATE TYPE mission_status AS ENUM ('active', 'completed', 'failed');
CREATE TYPE mission_type AS ENUM ('hourly');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  username TEXT UNIQUE NOT NULL,
  clan TEXT NOT NULL CHECK (clan IN (
    'Central and Western', 'Eastern', 'Southern', 'Wan Chai',
    'Sham Shui Po', 'Kowloon City', 'Kwun Tong', 'Wong Tai Sin',
    'Yau Tsim Mong', 'Islands', 'Kwai Tsing', 'North',
    'Sai Kung', 'Sha Tin', 'Tai Po', 'Tsuen Wan', 'Tuen Mun', 'Yuen Long'
  )),
  total_score INTEGER DEFAULT 0,
  login_streak INTEGER DEFAULT 0,
  missions_completed INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Missions table
CREATE TABLE public.missions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  mission_type mission_type DEFAULT 'hourly',
  target_clicks INTEGER NOT NULL CHECK (target_clicks > 0 AND target_clicks <= 60),
  completed_clicks INTEGER DEFAULT 0,
  status mission_status DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  score_awarded INTEGER DEFAULT 0,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User stats table (for caching and performance)
CREATE TABLE public.user_stats (
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE PRIMARY KEY,
  total_score INTEGER DEFAULT 0,
  missions_completed INTEGER DEFAULT 0,
  login_streak INTEGER DEFAULT 0,
  last_mission_completed TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Click tracking table (for anti-cheat)
CREATE TABLE public.click_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  mission_id UUID REFERENCES public.missions(id) ON DELETE CASCADE,
  clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT
);

-- Indexes for performance
CREATE INDEX idx_users_clan ON public.users(clan);
CREATE INDEX idx_users_total_score ON public.users(total_score DESC);
CREATE INDEX idx_missions_user_id ON public.missions(user_id);
CREATE INDEX idx_missions_status ON public.missions(status);
CREATE INDEX idx_missions_expires_at ON public.missions(expires_at);
CREATE INDEX idx_click_logs_user_id ON public.click_logs(user_id);
CREATE INDEX idx_click_logs_mission_id ON public.click_logs(mission_id);
CREATE INDEX idx_click_logs_clicked_at ON public.click_logs(clicked_at);

-- Row Level Security Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.missions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.click_logs ENABLE ROW LEVEL SECURITY;

-- Users can only see and edit their own data
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Users can only see their own missions
CREATE POLICY "Users can view own missions" ON public.missions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own missions" ON public.missions
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can only see their own stats
CREATE POLICY "Users can view own stats" ON public.user_stats
  FOR SELECT USING (auth.uid() = user_id);

-- Users can only see their own click logs
CREATE POLICY "Users can view own click logs" ON public.click_logs
  FOR SELECT USING (auth.uid() = user_id);

-- Functions and Triggers

-- Function to update user stats
CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'UPDATE' AND OLD.status != 'completed' AND NEW.status = 'completed' THEN
    -- Update user's total score and mission count
    UPDATE public.users 
    SET 
      total_score = total_score + NEW.score_awarded,
      missions_completed = missions_completed + 1,
      updated_at = NOW()
    WHERE id = NEW.user_id;
    
    -- Update user stats cache
    INSERT INTO public.user_stats (user_id, total_score, missions_completed, last_mission_completed)
    VALUES (NEW.user_id, 
            (SELECT total_score FROM public.users WHERE id = NEW.user_id),
            (SELECT missions_completed FROM public.users WHERE id = NEW.user_id),
            NOW())
    ON CONFLICT (user_id) 
    DO UPDATE SET
      total_score = EXCLUDED.total_score,
      missions_completed = EXCLUDED.missions_completed,
      last_mission_completed = EXCLUDED.last_mission_completed,
      updated_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update stats when mission is completed
CREATE TRIGGER trigger_update_user_stats
  AFTER UPDATE ON public.missions
  FOR EACH ROW
  EXECUTE FUNCTION update_user_stats();

-- Function to create user profile on signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, username, clan)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'clan', 'Central and Western')
  );
  
  INSERT INTO public.user_stats (user_id)
  VALUES (NEW.id);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to create user profile on auth signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user();

-- Function to update login streak
CREATE OR REPLACE FUNCTION update_login_streak(user_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
  last_login_date DATE;
  current_date DATE := CURRENT_DATE;
  new_streak INTEGER;
BEGIN
  SELECT DATE(last_login) INTO last_login_date
  FROM public.users
  WHERE id = user_uuid;
  
  IF last_login_date IS NULL OR last_login_date < current_date - INTERVAL '1 day' THEN
    -- Reset streak if more than 1 day gap
    new_streak := 1;
  ELSIF last_login_date = current_date - INTERVAL '1 day' THEN
    -- Increment streak if logged in yesterday
    SELECT login_streak + 1 INTO new_streak
    FROM public.users
    WHERE id = user_uuid;
  ELSE
    -- Same day login, keep current streak
    SELECT login_streak INTO new_streak
    FROM public.users
    WHERE id = user_uuid;
  END IF;
  
  UPDATE public.users
  SET 
    login_streak = new_streak,
    last_login = NOW(),
    updated_at = NOW()
  WHERE id = user_uuid;
  
  RETURN new_streak;
END;
$$ LANGUAGE plpgsql;

-- View for leaderboard
CREATE VIEW public.leaderboard AS
SELECT 
  u.id,
  u.username,
  u.clan,
  u.total_score,
  u.missions_completed,
  u.login_streak,
  ROW_NUMBER() OVER (ORDER BY u.total_score DESC) as global_rank,
  ROW_NUMBER() OVER (PARTITION BY u.clan ORDER BY u.total_score DESC) as clan_rank
FROM public.users u
WHERE u.total_score > 0
ORDER BY u.total_score DESC;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;
