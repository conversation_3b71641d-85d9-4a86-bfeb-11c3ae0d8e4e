// Generate simple audio tones using Web Audio API
export function generateTone(frequency: number, duration: number, volume: number = 0.3): string {
  if (typeof window === 'undefined') return ''
  
  try {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    const sampleRate = audioContext.sampleRate
    const numSamples = Math.floor(sampleRate * duration)
    const buffer = audioContext.createBuffer(1, numSamples, sampleRate)
    const channelData = buffer.getChannelData(0)

    // Generate a pleasant bell-like tone with harmonics
    for (let i = 0; i < numSamples; i++) {
      const t = i / sampleRate
      const envelope = Math.exp(-t * 3) // Exponential decay
      
      // Fundamental frequency + harmonics for a more pleasant sound
      const fundamental = Math.sin(2 * Math.PI * frequency * t)
      const harmonic2 = Math.sin(2 * Math.PI * frequency * 2 * t) * 0.3
      const harmonic3 = Math.sin(2 * Math.PI * frequency * 3 * t) * 0.1
      
      channelData[i] = (fundamental + harmonic2 + harmonic3) * envelope * volume
    }

    // Convert to WAV data URL
    return bufferToWav(buffer)
  } catch (error) {
    console.warn('Audio generation failed:', error)
    return ''
  }
}

// Convert AudioBuffer to WAV data URL
function bufferToWav(buffer: AudioBuffer): string {
  const length = buffer.length
  const arrayBuffer = new ArrayBuffer(44 + length * 2)
  const view = new DataView(arrayBuffer)
  const channelData = buffer.getChannelData(0)

  // WAV header
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i))
    }
  }

  writeString(0, 'RIFF')
  view.setUint32(4, 36 + length * 2, true)
  writeString(8, 'WAVE')
  writeString(12, 'fmt ')
  view.setUint32(16, 16, true)
  view.setUint16(20, 1, true)
  view.setUint16(22, 1, true)
  view.setUint32(24, buffer.sampleRate, true)
  view.setUint32(28, buffer.sampleRate * 2, true)
  view.setUint16(32, 2, true)
  view.setUint16(34, 16, true)
  writeString(36, 'data')
  view.setUint32(40, length * 2, true)

  // Convert float samples to 16-bit PCM
  let offset = 44
  for (let i = 0; i < length; i++) {
    const sample = Math.max(-1, Math.min(1, channelData[i]))
    view.setInt16(offset, sample * 0x7FFF, true)
    offset += 2
  }

  // Convert to base64 data URL
  const bytes = new Uint8Array(arrayBuffer)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  
  return 'data:audio/wav;base64,' + btoa(binary)
}

// Predefined sound effects for the wooden fish
export const WOODEN_FISH_SOUNDS = {
  // Pleasant bell-like tone for clicks
  click: () => generateTone(800, 0.2, 0.3),
  
  // Higher pitched tone for successful actions
  success: () => generateTone(1000, 0.3, 0.25),
  
  // Lower tone for errors/penalties
  error: () => generateTone(300, 0.5, 0.2),
  
  // Gentle chime for mission completion
  complete: () => generateTone(600, 0.4, 0.3)
}

// Initialize sounds (call this once when the app starts)
export function initializeSounds() {
  const sounds: Record<string, string> = {}
  
  Object.entries(WOODEN_FISH_SOUNDS).forEach(([key, generator]) => {
    try {
      sounds[key] = generator()
    } catch (error) {
      console.warn(`Failed to generate ${key} sound:`, error)
      sounds[key] = '' // Fallback to empty string
    }
  })
  
  return sounds
}
