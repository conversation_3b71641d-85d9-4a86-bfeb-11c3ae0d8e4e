{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { User } from '@/lib/database'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogOut, Trophy, User as UserIcon } from 'lucide-react'\n\ninterface NavigationProps {\n  user: User | null\n}\n\nexport default function Navigation({ user }: NavigationProps) {\n  const { signOut } = useAuth()\n  const [loading, setLoading] = useState(false)\n\n  const handleSignOut = async () => {\n    setLoading(true)\n    await signOut()\n    setLoading(false)\n  }\n\n  return (\n    <nav className=\"bg-white/80 backdrop-blur-sm border-b border-amber-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"text-2xl\">🪵🐟</div>\n            <div className=\"font-bold text-amber-800\">\n              <span className=\"hidden sm:inline\">木魚功德</span>\n              <span className=\"sm:hidden\">木魚</span>\n            </div>\n          </div>\n\n          {/* User Info */}\n          {user ? (\n            <div className=\"flex items-center space-x-4\">\n              {/* Score Display */}\n              <div className=\"hidden sm:flex items-center space-x-2 bg-amber-100 px-3 py-1 rounded-full\">\n                <Trophy className=\"w-4 h-4 text-amber-600\" />\n                <span className=\"text-sm font-medium text-amber-800\">\n                  {user.total_score}\n                </span>\n              </div>\n\n              {/* Clan Badge */}\n              <div className=\"hidden md:flex items-center space-x-1 bg-blue-100 px-2 py-1 rounded-full\">\n                <span className=\"text-xs font-medium text-blue-800\">\n                  {user.clan_district}\n                </span>\n              </div>\n\n              {/* User Menu */}\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"flex items-center space-x-1\">\n                  <UserIcon className=\"w-4 h-4 text-gray-600\" />\n                  <span className=\"text-sm font-medium text-gray-700 hidden sm:inline\">\n                    {user.display_name || user.username}\n                  </span>\n                </div>\n\n                <button\n                  onClick={handleSignOut}\n                  disabled={loading}\n                  className=\"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors\"\n                  title=\"Sign Out\"\n                >\n                  <LogOut className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-sm text-gray-600\">\n              Welcome to 木魚功德\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Mobile Score Display */}\n      {user && (\n        <div className=\"sm:hidden bg-amber-50 px-4 py-2 border-t border-amber-200\">\n          <div className=\"flex justify-between items-center text-sm\">\n            <div className=\"flex items-center space-x-2\">\n              <Trophy className=\"w-4 h-4 text-amber-600\" />\n              <span className=\"font-medium text-amber-800\">\n                Score: {user.total_score}\n              </span>\n            </div>\n            <div className=\"text-blue-800 font-medium\">\n              {user.clan_district}\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AALA;;;;;AAWe,SAAS,WAAW,EAAE,IAAI,EAAmB;IAC1D,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,gBAAgB;QACpB,WAAW;QACX,MAAM;QACN,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAW;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,8OAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;;;;;;;wBAK/B,qBACC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDACb,KAAK,WAAW;;;;;;;;;;;;8CAKrB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,KAAK,aAAa;;;;;;;;;;;8CAKvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DACb,KAAK,YAAY,IAAI,KAAK,QAAQ;;;;;;;;;;;;sDAIvC,8OAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;iDAKxB,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAQ5C,sBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;;wCAA6B;wCACnC,KAAK,WAAW;;;;;;;;;;;;;sCAG5B,8OAAC;4BAAI,WAAU;sCACZ,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;;AAOjC", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/components/GameLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode } from 'react'\nimport Navigation from './Navigation'\nimport { User } from '@/lib/types'\n\ninterface GameLayoutProps {\n  children: ReactNode\n  user: User | null\n}\n\nexport default function GameLayout({ children, user }: GameLayoutProps) {\n  return (\n    <>\n      <Navigation user={user} />\n      <main className=\"flex-1 flex flex-col\">\n        {children}\n      </main>\n      <footer className=\"bg-white/60 backdrop-blur-sm border-t border-amber-200 py-4\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center text-sm text-gray-600\">\n            <p>木魚功德 - Traditional Merit Accumulation Game</p>\n            <p className=\"mt-1 text-xs\">\n              Click mindfully • Complete missions • Build merit\n            </p>\n          </div>\n        </div>\n      </footer>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWe,SAAS,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAmB;IACpE,qBACE;;0BACE,8OAAC,gIAAA,CAAA,UAAU;gBAAC,MAAM;;;;;;0BAClB,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;;;AAQxC", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/components/WoodenFish.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback, useRef, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\n// Simple audio implementation without complex generation\n\ninterface ClickEffect {\n  id: string\n  x: number\n  y: number\n  timestamp: number\n}\n\ninterface WoodenFishProps {\n  onFishClick: (clickData: { x: number; y: number; timestamp: number }) => void\n  disabled?: boolean\n  clickCount?: number\n  soundEnabled?: boolean\n}\n\nexport default function WoodenFish({\n  onFishClick,\n  disabled = false,\n  clickCount = 0,\n  soundEnabled = true\n}: WoodenFishProps) {\n  const [clickEffects, setClickEffects] = useState<ClickEffect[]>([])\n  const [isPressed, setIsPressed] = useState(false)\n  const fishRef = useRef<HTMLDivElement>(null)\n  const clickIdRef = useRef(0)\n\n  // Simple audio system using Web Audio API\n  const audioContextRef = useRef<AudioContext | null>(null)\n\n  // Initialize audio context\n  useEffect(() => {\n    if (soundEnabled && typeof window !== 'undefined') {\n      try {\n        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()\n      } catch (error) {\n        console.warn('Audio context not supported:', error)\n      }\n    }\n  }, [soundEnabled])\n\n  // Simple click sound generator\n  const playClickSound = useCallback(() => {\n    if (!soundEnabled || !audioContextRef.current) return\n\n    try {\n      const ctx = audioContextRef.current\n      const oscillator = ctx.createOscillator()\n      const gainNode = ctx.createGain()\n\n      oscillator.connect(gainNode)\n      gainNode.connect(ctx.destination)\n\n      // Pleasant bell-like frequency\n      oscillator.frequency.setValueAtTime(800, ctx.currentTime)\n      oscillator.type = 'sine'\n\n      // Quick fade out\n      gainNode.gain.setValueAtTime(0.1, ctx.currentTime)\n      gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.2)\n\n      oscillator.start(ctx.currentTime)\n      oscillator.stop(ctx.currentTime + 0.2)\n    } catch (error) {\n      console.warn('Failed to play sound:', error)\n    }\n  }, [soundEnabled])\n\n  // Clean up old click effects\n  useEffect(() => {\n    const interval = setInterval(() => {\n      const now = Date.now()\n      setClickEffects(prev => prev.filter(effect => now - effect.timestamp < 2000))\n    }, 100)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const handleClick = useCallback((event: React.MouseEvent | React.TouchEvent) => {\n    if (disabled) return\n\n    event.preventDefault()\n    \n    const rect = fishRef.current?.getBoundingClientRect()\n    if (!rect) return\n\n    // Get click/touch position\n    let clientX: number, clientY: number\n    \n    if ('touches' in event) {\n      // Touch event\n      const touch = event.touches[0] || event.changedTouches[0]\n      clientX = touch.clientX\n      clientY = touch.clientY\n    } else {\n      // Mouse event\n      clientX = event.clientX\n      clientY = event.clientY\n    }\n\n    // Calculate relative position within the fish component\n    const x = clientX - rect.left\n    const y = clientY - rect.top\n\n    const timestamp = Date.now()\n    const clickId = `click-${++clickIdRef.current}-${timestamp}`\n\n    // Add click effect\n    setClickEffects(prev => [...prev, {\n      id: clickId,\n      x,\n      y,\n      timestamp\n    }])\n\n    // Play click sound\n    playClickSound()\n\n    // Call the parent callback\n    onFishClick({ x, y, timestamp })\n  }, [disabled, onFishClick])\n\n  const handleMouseDown = useCallback(() => {\n    if (!disabled) setIsPressed(true)\n  }, [disabled])\n\n  const handleMouseUp = useCallback(() => {\n    setIsPressed(false)\n  }, [])\n\n  const handleTouchStart = useCallback((event: React.TouchEvent) => {\n    if (!disabled) {\n      setIsPressed(true)\n      // Prevent default to avoid mouse events on touch devices\n      event.preventDefault()\n    }\n  }, [disabled])\n\n  const handleTouchEnd = useCallback((event: React.TouchEvent) => {\n    setIsPressed(false)\n    // Handle the click on touch end\n    handleClick(event)\n  }, [handleClick])\n\n  return (\n    <div className=\"relative flex items-center justify-center\">\n      {/* Wooden Fish Container */}\n      <motion.div\n        ref={fishRef}\n        className={`\n          relative select-none cursor-pointer\n          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}\n          transition-opacity duration-200\n        `}\n        animate={{\n          scale: isPressed ? 0.95 : 1,\n          rotate: isPressed ? -2 : 0\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 400,\n          damping: 25\n        }}\n        onClick={handleClick}\n        onMouseDown={handleMouseDown}\n        onMouseUp={handleMouseUp}\n        onMouseLeave={handleMouseUp}\n        onTouchStart={handleTouchStart}\n        onTouchEnd={handleTouchEnd}\n        style={{\n          touchAction: 'manipulation' // Prevent double-tap zoom on mobile\n        }}\n      >\n        {/* Wooden Fish Emoji */}\n        <div className=\"text-8xl sm:text-9xl md:text-[12rem] leading-none\">\n          🪵🐟\n        </div>\n\n        {/* Glow Effect */}\n        <motion.div\n          className=\"absolute inset-0 rounded-full\"\n          animate={{\n            boxShadow: isPressed \n              ? '0 0 30px rgba(245, 158, 11, 0.6), 0 0 60px rgba(245, 158, 11, 0.3)'\n              : '0 0 20px rgba(245, 158, 11, 0.3)'\n          }}\n          transition={{ duration: 0.2 }}\n        />\n\n        {/* Click Effects */}\n        <AnimatePresence>\n          {clickEffects.map((effect) => (\n            <motion.div\n              key={effect.id}\n              className=\"absolute pointer-events-none z-10\"\n              style={{\n                left: effect.x,\n                top: effect.y,\n                transform: 'translate(-50%, -50%)'\n              }}\n              initial={{\n                opacity: 1,\n                scale: 0.5,\n                y: 0\n              }}\n              animate={{\n                opacity: 0,\n                scale: 1.5,\n                y: -50\n              }}\n              exit={{\n                opacity: 0,\n                scale: 0.5\n              }}\n              transition={{\n                duration: 1.5,\n                ease: \"easeOut\"\n              }}\n            >\n              <div className=\"text-2xl font-bold text-amber-600 drop-shadow-lg\">\n                功德+1\n              </div>\n            </motion.div>\n          ))}\n        </AnimatePresence>\n      </motion.div>\n\n      {/* Click Counter Display */}\n      {clickCount > 0 && (\n        <motion.div\n          className=\"absolute -bottom-8 left-1/2 transform -translate-x-1/2\"\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <div className=\"bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm font-medium\">\n            Clicks: {clickCount}\n          </div>\n        </motion.div>\n      )}\n\n      {/* Instructions */}\n      <motion.div\n        className=\"absolute -bottom-16 left-1/2 transform -translate-x-1/2 text-center\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.5 }}\n      >\n        <p className=\"text-sm text-amber-600 font-medium\">\n          {disabled ? 'Wooden fish is resting...' : 'Click or tap to gain merit!'}\n        </p>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAoBe,SAAS,WAAW,EACjC,WAAW,EACX,WAAW,KAAK,EAChB,aAAa,CAAC,EACd,eAAe,IAAI,EACH;IAChB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,0CAA0C;IAC1C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAEpD,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmD;;QAMnD;IACF,GAAG;QAAC;KAAa;IAEjB,+BAA+B;IAC/B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,OAAO,EAAE;QAE/C,IAAI;YACF,MAAM,MAAM,gBAAgB,OAAO;YACnC,MAAM,aAAa,IAAI,gBAAgB;YACvC,MAAM,WAAW,IAAI,UAAU;YAE/B,WAAW,OAAO,CAAC;YACnB,SAAS,OAAO,CAAC,IAAI,WAAW;YAEhC,+BAA+B;YAC/B,WAAW,SAAS,CAAC,cAAc,CAAC,KAAK,IAAI,WAAW;YACxD,WAAW,IAAI,GAAG;YAElB,iBAAiB;YACjB,SAAS,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,WAAW;YACjD,SAAS,IAAI,CAAC,4BAA4B,CAAC,MAAM,IAAI,WAAW,GAAG;YAEnE,WAAW,KAAK,CAAC,IAAI,WAAW;YAChC,WAAW,IAAI,CAAC,IAAI,WAAW,GAAG;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,yBAAyB;QACxC;IACF,GAAG;QAAC;KAAa;IAEjB,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,MAAM,MAAM,KAAK,GAAG;YACpB,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,SAAU,MAAM,OAAO,SAAS,GAAG;QACzE,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,IAAI,UAAU;QAEd,MAAM,cAAc;QAEpB,MAAM,OAAO,QAAQ,OAAO,EAAE;QAC9B,IAAI,CAAC,MAAM;QAEX,2BAA2B;QAC3B,IAAI,SAAiB;QAErB,IAAI,aAAa,OAAO;YACtB,cAAc;YACd,MAAM,QAAQ,MAAM,OAAO,CAAC,EAAE,IAAI,MAAM,cAAc,CAAC,EAAE;YACzD,UAAU,MAAM,OAAO;YACvB,UAAU,MAAM,OAAO;QACzB,OAAO;YACL,cAAc;YACd,UAAU,MAAM,OAAO;YACvB,UAAU,MAAM,OAAO;QACzB;QAEA,wDAAwD;QACxD,MAAM,IAAI,UAAU,KAAK,IAAI;QAC7B,MAAM,IAAI,UAAU,KAAK,GAAG;QAE5B,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,UAAU,CAAC,MAAM,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,EAAE,WAAW;QAE5D,mBAAmB;QACnB,gBAAgB,CAAA,OAAQ;mBAAI;gBAAM;oBAChC,IAAI;oBACJ;oBACA;oBACA;gBACF;aAAE;QAEF,mBAAmB;QACnB;QAEA,2BAA2B;QAC3B,YAAY;YAAE;YAAG;YAAG;QAAU;IAChC,GAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,CAAC,UAAU,aAAa;IAC9B,GAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,CAAC,UAAU;YACb,aAAa;YACb,yDAAyD;YACzD,MAAM,cAAc;QACtB;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,aAAa;QACb,gCAAgC;QAChC,YAAY;IACd,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,KAAK;gBACL,WAAW,CAAC;;UAEV,EAAE,WAAW,kCAAkC,kBAAkB;;QAEnE,CAAC;gBACD,SAAS;oBACP,OAAO,YAAY,OAAO;oBAC1B,QAAQ,YAAY,CAAC,IAAI;gBAC3B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;gBACA,SAAS;gBACT,aAAa;gBACb,WAAW;gBACX,cAAc;gBACd,cAAc;gBACd,YAAY;gBACZ,OAAO;oBACL,aAAa,eAAe,oCAAoC;gBAClE;;kCAGA,8OAAC;wBAAI,WAAU;kCAAoD;;;;;;kCAKnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,WAAW,YACP,uEACA;wBACN;wBACA,YAAY;4BAAE,UAAU;wBAAI;;;;;;kCAI9B,8OAAC,yLAAA,CAAA,kBAAe;kCACb,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,MAAM,OAAO,CAAC;oCACd,KAAK,OAAO,CAAC;oCACb,WAAW;gCACb;gCACA,SAAS;oCACP,SAAS;oCACT,OAAO;oCACP,GAAG;gCACL;gCACA,SAAS;oCACP,SAAS;oCACT,OAAO;oCACP,GAAG,CAAC;gCACN;gCACA,MAAM;oCACJ,SAAS;oCACT,OAAO;gCACT;gCACA,YAAY;oCACV,UAAU;oCACV,MAAM;gCACR;0CAEA,cAAA,8OAAC;oCAAI,WAAU;8CAAmD;;;;;;+BA1B7D,OAAO,EAAE;;;;;;;;;;;;;;;;YAmCrB,aAAa,mBACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,8OAAC;oBAAI,WAAU;;wBAAyE;wBAC7E;;;;;;;;;;;;0BAMf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,8OAAC;oBAAE,WAAU;8BACV,WAAW,8BAA8B;;;;;;;;;;;;;;;;;AAKpD", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/lib/missionUtils.ts"], "sourcesContent": ["import { format, isWithinInterval, startOfHour, addHours, parseISO } from 'date-fns'\n\nexport interface Mission {\n  id: string\n  user_id: string\n  mission_type: 'hourly'\n  target_clicks: number\n  completed_clicks: number\n  status: 'active' | 'completed' | 'failed'\n  created_at: string\n  expires_at: string\n  score_awarded: number\n}\n\nexport interface MissionProgress {\n  currentMission: Mission | null\n  clicksRemaining: number\n  timeRemaining: number\n  canClick: boolean\n  penalty: number\n}\n\n// Hong Kong timezone offset (UTC+8)\nconst HK_TIMEZONE_OFFSET = 8 * 60 * 60 * 1000\n\n// Get current Hong Kong time\nexport function getHKTime(): Date {\n  const now = new Date()\n  return new Date(now.getTime() + HK_TIMEZONE_OFFSET)\n}\n\n// Check if current time is within active hours (08:00-22:59 HK time)\nexport function isWithinActiveHours(date: Date = getHKTime()): boolean {\n  const hkTime = new Date(date.getTime() + HK_TIMEZONE_OFFSET)\n  const hour = hkTime.getHours()\n  return hour >= 8 && hour <= 22\n}\n\n// Get the current hour slot for mission generation\nexport function getCurrentHourSlot(date: Date = getHKTime()): string {\n  const hkTime = new Date(date.getTime() + HK_TIMEZONE_OFFSET)\n  const hourStart = startOfHour(hkTime)\n  return format(hourStart, 'yyyy-MM-dd HH:00:00')\n}\n\n// Generate mission for a specific hour slot\nexport function generateMissionForHour(userId: string, hourSlot: string, missionIndex: number): Omit<Mission, 'id'> {\n  const hourStart = parseISO(hourSlot.replace(' ', 'T') + 'Z')\n  const hourEnd = addHours(hourStart, 1)\n  \n  // Generate target clicks (1-60 based on mission index and hour)\n  const baseClicks = 10 + (missionIndex * 15) // 10, 25, 40 for missions 1, 2, 3\n  const hourVariation = Math.floor(Math.random() * 21) - 10 // -10 to +10\n  const targetClicks = Math.max(1, Math.min(60, baseClicks + hourVariation))\n\n  return {\n    user_id: userId,\n    mission_type: 'hourly',\n    target_clicks: targetClicks,\n    completed_clicks: 0,\n    status: 'active',\n    created_at: new Date().toISOString(),\n    expires_at: hourEnd.toISOString(),\n    score_awarded: 0\n  }\n}\n\n// Calculate score for completed mission\nexport function calculateMissionScore(mission: Mission): number {\n  if (mission.status !== 'completed') return 0\n  \n  const baseScore = mission.target_clicks\n  const efficiency = mission.completed_clicks === mission.target_clicks ? 1.5 : 1.0\n  \n  return Math.floor(baseScore * efficiency)\n}\n\n// Calculate penalty for exceeding clicks\nexport function calculateClickPenalty(targetClicks: number, actualClicks: number): number {\n  if (actualClicks <= targetClicks) return 0\n  return -100 // Fixed penalty for exceeding target\n}\n\n// Validate click against mission requirements\nexport function validateClick(mission: Mission, newClickCount: number): {\n  isValid: boolean\n  newStatus: Mission['status']\n  penalty: number\n  scoreAwarded: number\n} {\n  if (!mission || mission.status !== 'active') {\n    return {\n      isValid: false,\n      newStatus: mission?.status || 'failed',\n      penalty: 0,\n      scoreAwarded: 0\n    }\n  }\n\n  // Check if mission has expired\n  const now = new Date()\n  const expiresAt = new Date(mission.expires_at)\n  if (now > expiresAt) {\n    return {\n      isValid: false,\n      newStatus: 'failed',\n      penalty: 0,\n      scoreAwarded: 0\n    }\n  }\n\n  const updatedMission = { ...mission, completed_clicks: newClickCount }\n\n  // Check if mission is completed\n  if (newClickCount >= mission.target_clicks) {\n    const penalty = calculateClickPenalty(mission.target_clicks, newClickCount)\n    const baseScore = calculateMissionScore({ ...updatedMission, status: 'completed' })\n    const scoreAwarded = Math.max(0, baseScore + penalty)\n\n    return {\n      isValid: true,\n      newStatus: 'completed',\n      penalty,\n      scoreAwarded\n    }\n  }\n\n  // Mission still in progress\n  return {\n    isValid: true,\n    newStatus: 'active',\n    penalty: 0,\n    scoreAwarded: 0\n  }\n}\n\n// Get missions that should be generated for current hour\nexport function getMissionsForCurrentHour(userId: string): Array<Omit<Mission, 'id'>> {\n  if (!isWithinActiveHours()) {\n    return []\n  }\n\n  const currentHourSlot = getCurrentHourSlot()\n  const missions: Array<Omit<Mission, 'id'>> = []\n\n  // Generate 3 missions per hour\n  for (let i = 0; i < 3; i++) {\n    missions.push(generateMissionForHour(userId, currentHourSlot, i))\n  }\n\n  return missions\n}\n\n// Format time remaining for display\nexport function formatTimeRemaining(expiresAt: string): string {\n  const now = new Date()\n  const expires = new Date(expiresAt)\n  const diff = expires.getTime() - now.getTime()\n\n  if (diff <= 0) return 'Expired'\n\n  const minutes = Math.floor(diff / (1000 * 60))\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000)\n\n  if (minutes > 0) {\n    return `${minutes}m ${seconds}s`\n  }\n  return `${seconds}s`\n}\n\n// Get next mission generation time\nexport function getNextMissionTime(): Date {\n  const hkTime = getHKTime()\n  const currentHour = hkTime.getHours()\n  \n  // If before 8 AM, next missions at 8 AM\n  if (currentHour < 8) {\n    const nextMissionTime = new Date(hkTime)\n    nextMissionTime.setHours(8, 0, 0, 0)\n    return nextMissionTime\n  }\n  \n  // If after 10 PM, next missions at 8 AM tomorrow\n  if (currentHour >= 23) {\n    const nextMissionTime = new Date(hkTime)\n    nextMissionTime.setDate(nextMissionTime.getDate() + 1)\n    nextMissionTime.setHours(8, 0, 0, 0)\n    return nextMissionTime\n  }\n  \n  // Otherwise, next hour\n  const nextMissionTime = new Date(hkTime)\n  nextMissionTime.setHours(currentHour + 1, 0, 0, 0)\n  return nextMissionTime\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;;AAsBA,oCAAoC;AACpC,MAAM,qBAAqB,IAAI,KAAK,KAAK;AAGlC,SAAS;IACd,MAAM,MAAM,IAAI;IAChB,OAAO,IAAI,KAAK,IAAI,OAAO,KAAK;AAClC;AAGO,SAAS,oBAAoB,OAAa,WAAW;IAC1D,MAAM,SAAS,IAAI,KAAK,KAAK,OAAO,KAAK;IACzC,MAAM,OAAO,OAAO,QAAQ;IAC5B,OAAO,QAAQ,KAAK,QAAQ;AAC9B;AAGO,SAAS,mBAAmB,OAAa,WAAW;IACzD,MAAM,SAAS,IAAI,KAAK,KAAK,OAAO,KAAK;IACzC,MAAM,YAAY,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE;IAC9B,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;AAC3B;AAGO,SAAS,uBAAuB,MAAc,EAAE,QAAgB,EAAE,YAAoB;IAC3F,MAAM,YAAY,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,CAAC,KAAK,OAAO;IACxD,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;IAEpC,gEAAgE;IAChE,MAAM,aAAa,KAAM,eAAe,GAAI,kCAAkC;;IAC9E,MAAM,gBAAgB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,aAAa;;IACvE,MAAM,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,aAAa;IAE3D,OAAO;QACL,SAAS;QACT,cAAc;QACd,eAAe;QACf,kBAAkB;QAClB,QAAQ;QACR,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY,QAAQ,WAAW;QAC/B,eAAe;IACjB;AACF;AAGO,SAAS,sBAAsB,OAAgB;IACpD,IAAI,QAAQ,MAAM,KAAK,aAAa,OAAO;IAE3C,MAAM,YAAY,QAAQ,aAAa;IACvC,MAAM,aAAa,QAAQ,gBAAgB,KAAK,QAAQ,aAAa,GAAG,MAAM;IAE9E,OAAO,KAAK,KAAK,CAAC,YAAY;AAChC;AAGO,SAAS,sBAAsB,YAAoB,EAAE,YAAoB;IAC9E,IAAI,gBAAgB,cAAc,OAAO;IACzC,OAAO,CAAC,IAAI,qCAAqC;;AACnD;AAGO,SAAS,cAAc,OAAgB,EAAE,aAAqB;IAMnE,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,UAAU;QAC3C,OAAO;YACL,SAAS;YACT,WAAW,SAAS,UAAU;YAC9B,SAAS;YACT,cAAc;QAChB;IACF;IAEA,+BAA+B;IAC/B,MAAM,MAAM,IAAI;IAChB,MAAM,YAAY,IAAI,KAAK,QAAQ,UAAU;IAC7C,IAAI,MAAM,WAAW;QACnB,OAAO;YACL,SAAS;YACT,WAAW;YACX,SAAS;YACT,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB;QAAE,GAAG,OAAO;QAAE,kBAAkB;IAAc;IAErE,gCAAgC;IAChC,IAAI,iBAAiB,QAAQ,aAAa,EAAE;QAC1C,MAAM,UAAU,sBAAsB,QAAQ,aAAa,EAAE;QAC7D,MAAM,YAAY,sBAAsB;YAAE,GAAG,cAAc;YAAE,QAAQ;QAAY;QACjF,MAAM,eAAe,KAAK,GAAG,CAAC,GAAG,YAAY;QAE7C,OAAO;YACL,SAAS;YACT,WAAW;YACX;YACA;QACF;IACF;IAEA,4BAA4B;IAC5B,OAAO;QACL,SAAS;QACT,WAAW;QACX,SAAS;QACT,cAAc;IAChB;AACF;AAGO,SAAS,0BAA0B,MAAc;IACtD,IAAI,CAAC,uBAAuB;QAC1B,OAAO,EAAE;IACX;IAEA,MAAM,kBAAkB;IACxB,MAAM,WAAuC,EAAE;IAE/C,+BAA+B;IAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,SAAS,IAAI,CAAC,uBAAuB,QAAQ,iBAAiB;IAChE;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,SAAiB;IACnD,MAAM,MAAM,IAAI;IAChB,MAAM,UAAU,IAAI,KAAK;IACzB,MAAM,OAAO,QAAQ,OAAO,KAAK,IAAI,OAAO;IAE5C,IAAI,QAAQ,GAAG,OAAO;IAEtB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE;IAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,IAAI,UAAU,GAAG;QACf,OAAO,GAAG,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;IAClC;IACA,OAAO,GAAG,QAAQ,CAAC,CAAC;AACtB;AAGO,SAAS;IACd,MAAM,SAAS;IACf,MAAM,cAAc,OAAO,QAAQ;IAEnC,wCAAwC;IACxC,IAAI,cAAc,GAAG;QACnB,MAAM,kBAAkB,IAAI,KAAK;QACjC,gBAAgB,QAAQ,CAAC,GAAG,GAAG,GAAG;QAClC,OAAO;IACT;IAEA,iDAAiD;IACjD,IAAI,eAAe,IAAI;QACrB,MAAM,kBAAkB,IAAI,KAAK;QACjC,gBAAgB,OAAO,CAAC,gBAAgB,OAAO,KAAK;QACpD,gBAAgB,QAAQ,CAAC,GAAG,GAAG,GAAG;QAClC,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,kBAAkB,IAAI,KAAK;IACjC,gBAAgB,QAAQ,CAAC,cAAc,GAAG,GAAG,GAAG;IAChD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 809, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/components/MissionPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Clock, Target, CheckCircle, XCircle, AlertTriangle } from 'lucide-react'\nimport { Mission } from '@/lib/missionUtils'\nimport { formatTimeRemaining } from '@/lib/missionUtils'\n\ninterface MissionPanelProps {\n  userId: string\n  onMissionSelect: (mission: Mission | null) => void\n  onMissionUpdate: (mission: Mission) => void\n}\n\ninterface MissionResponse {\n  missions: Mission[]\n  activeHours: boolean\n  nextMissionTime?: string\n  message?: string\n  currentHourSlot?: string\n}\n\nexport default function MissionPanel({ userId, onMissionSelect, onMissionUpdate }: MissionPanelProps) {\n  const [missions, setMissions] = useState<Mission[]>([])\n  const [selectedMission, setSelectedMission] = useState<Mission | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [activeHours, setActiveHours] = useState(true)\n  const [nextMissionTime, setNextMissionTime] = useState<string | null>(null)\n\n  // Fetch missions from API\n  const fetchMissions = useCallback(async () => {\n    try {\n      setLoading(true)\n      const response = await fetch('/api/missions')\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch missions')\n      }\n\n      const data: MissionResponse = await response.json()\n      \n      setMissions(data.missions)\n      setActiveHours(data.activeHours)\n      setNextMissionTime(data.nextMissionTime || null)\n      \n      // Auto-select first active mission\n      const activeMission = data.missions.find(m => m.status === 'active')\n      if (activeMission && (!selectedMission || selectedMission.status !== 'active')) {\n        setSelectedMission(activeMission)\n        onMissionSelect(activeMission)\n      }\n      \n      setError(null)\n    } catch (err) {\n      console.error('Error fetching missions:', err)\n      setError('Failed to load missions')\n    } finally {\n      setLoading(false)\n    }\n  }, [selectedMission, onMissionSelect])\n\n  // Initial fetch and periodic updates\n  useEffect(() => {\n    fetchMissions()\n    \n    // Refresh missions every 30 seconds\n    const interval = setInterval(fetchMissions, 30000)\n    \n    return () => clearInterval(interval)\n  }, [fetchMissions])\n\n  // Handle mission selection\n  const handleMissionSelect = (mission: Mission) => {\n    if (mission.status === 'active') {\n      setSelectedMission(mission)\n      onMissionSelect(mission)\n    }\n  }\n\n  // Update mission when it changes\n  const handleMissionUpdate = (updatedMission: Mission) => {\n    setMissions(prev => prev.map(m => \n      m.id === updatedMission.id ? updatedMission : m\n    ))\n    \n    if (selectedMission?.id === updatedMission.id) {\n      setSelectedMission(updatedMission)\n    }\n    \n    onMissionUpdate(updatedMission)\n  }\n\n  // Get status icon\n  const getStatusIcon = (status: Mission['status']) => {\n    switch (status) {\n      case 'active':\n        return <Target className=\"w-4 h-4 text-blue-500\" />\n      case 'completed':\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />\n      case 'failed':\n        return <XCircle className=\"w-4 h-4 text-red-500\" />\n      default:\n        return <AlertTriangle className=\"w-4 h-4 text-gray-500\" />\n    }\n  }\n\n  // Get status color\n  const getStatusColor = (status: Mission['status']) => {\n    switch (status) {\n      case 'active':\n        return 'border-blue-200 bg-blue-50'\n      case 'completed':\n        return 'border-green-200 bg-green-50'\n      case 'failed':\n        return 'border-red-200 bg-red-50'\n      default:\n        return 'border-gray-200 bg-gray-50'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200\">\n        <div className=\"flex items-center justify-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600\"></div>\n          <span className=\"ml-2 text-amber-700\">Loading missions...</span>\n        </div>\n      </div>\n    )\n  }\n\n  if (!activeHours) {\n    return (\n      <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200\">\n        <div className=\"text-center\">\n          <Clock className=\"w-12 h-12 text-amber-600 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-amber-800 mb-2\">\n            Missions Unavailable\n          </h3>\n          <p className=\"text-amber-700 mb-4\">\n            Missions are only available between 08:00-22:59 HK time\n          </p>\n          {nextMissionTime && (\n            <p className=\"text-sm text-amber-600\">\n              Next missions available: {new Date(nextMissionTime).toLocaleString()}\n            </p>\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-red-200\">\n        <div className=\"text-center\">\n          <AlertTriangle className=\"w-12 h-12 text-red-600 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-red-800 mb-2\">\n            Error Loading Missions\n          </h3>\n          <p className=\"text-red-700 mb-4\">{error}</p>\n          <button\n            onClick={fetchMissions}\n            className=\"px-4 py-2 bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-200\">\n      <h3 className=\"text-lg font-semibold text-amber-800 mb-4 flex items-center\">\n        <Target className=\"w-5 h-5 mr-2\" />\n        Current Missions\n      </h3>\n\n      <div className=\"space-y-3\">\n        <AnimatePresence>\n          {missions.map((mission, index) => (\n            <motion.div\n              key={mission.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ delay: index * 0.1 }}\n              className={`\n                p-4 rounded-lg border-2 cursor-pointer transition-all\n                ${getStatusColor(mission.status)}\n                ${selectedMission?.id === mission.id ? 'ring-2 ring-amber-400' : ''}\n                ${mission.status === 'active' ? 'hover:shadow-md' : 'opacity-75'}\n              `}\n              onClick={() => handleMissionSelect(mission)}\n            >\n              <div className=\"flex items-center justify-between mb-2\">\n                <div className=\"flex items-center\">\n                  {getStatusIcon(mission.status)}\n                  <span className=\"ml-2 font-medium text-gray-800\">\n                    Mission {index + 1}\n                  </span>\n                </div>\n                <div className=\"text-sm text-gray-600\">\n                  {mission.status === 'active' && (\n                    <span className=\"flex items-center\">\n                      <Clock className=\"w-3 h-3 mr-1\" />\n                      {formatTimeRemaining(mission.expires_at)}\n                    </span>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"text-sm\">\n                  <span className=\"text-gray-600\">Target: </span>\n                  <span className=\"font-medium\">{mission.target_clicks} clicks</span>\n                </div>\n                <div className=\"text-sm\">\n                  <span className=\"text-gray-600\">Progress: </span>\n                  <span className=\"font-medium\">\n                    {mission.completed_clicks}/{mission.target_clicks}\n                  </span>\n                </div>\n              </div>\n\n              {mission.status === 'completed' && mission.score_awarded > 0 && (\n                <div className=\"mt-2 text-sm text-green-600 font-medium\">\n                  +{mission.score_awarded} points earned\n                </div>\n              )}\n\n              {mission.status === 'active' && (\n                <div className=\"mt-2\">\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div\n                      className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\n                      style={{\n                        width: `${Math.min(100, (mission.completed_clicks / mission.target_clicks) * 100)}%`\n                      }}\n                    />\n                  </div>\n                </div>\n              )}\n            </motion.div>\n          ))}\n        </AnimatePresence>\n      </div>\n\n      {selectedMission && selectedMission.status === 'active' && (\n        <div className=\"mt-4 p-3 bg-amber-50 rounded-lg border border-amber-200\">\n          <p className=\"text-sm text-amber-800\">\n            <strong>Active Mission:</strong> Click the wooden fish {' '}\n            {selectedMission.target_clicks - selectedMission.completed_clicks} more times\n            {selectedMission.completed_clicks > selectedMission.target_clicks && (\n              <span className=\"text-red-600 font-medium\">\n                {' '}(Warning: Exceeding target will result in -100 penalty!)\n              </span>\n            )}\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;AAsBe,SAAS,aAAa,EAAE,MAAM,EAAE,eAAe,EAAE,eAAe,EAAqB;IAClG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAwB,MAAM,SAAS,IAAI;YAEjD,YAAY,KAAK,QAAQ;YACzB,eAAe,KAAK,WAAW;YAC/B,mBAAmB,KAAK,eAAe,IAAI;YAE3C,mCAAmC;YACnC,MAAM,gBAAgB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAC3D,IAAI,iBAAiB,CAAC,CAAC,mBAAmB,gBAAgB,MAAM,KAAK,QAAQ,GAAG;gBAC9E,mBAAmB;gBACnB,gBAAgB;YAClB;YAEA,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAiB;KAAgB;IAErC,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,oCAAoC;QACpC,MAAM,WAAW,YAAY,eAAe;QAE5C,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAc;IAElB,2BAA2B;IAC3B,MAAM,sBAAsB,CAAC;QAC3B,IAAI,QAAQ,MAAM,KAAK,UAAU;YAC/B,mBAAmB;YACnB,gBAAgB;QAClB;IACF;IAEA,iCAAiC;IACjC,MAAM,sBAAsB,CAAC;QAC3B,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC3B,EAAE,EAAE,KAAK,eAAe,EAAE,GAAG,iBAAiB;QAGhD,IAAI,iBAAiB,OAAO,eAAe,EAAE,EAAE;YAC7C,mBAAmB;QACrB;QAEA,gBAAgB;IAClB;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;QACpC;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAK,WAAU;kCAAsB;;;;;;;;;;;;;;;;;IAI9C;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAG,WAAU;kCAA4C;;;;;;kCAG1D,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;oBAGlC,iCACC,8OAAC;wBAAE,WAAU;;4BAAyB;4BACV,IAAI,KAAK,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;IAM9E;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCAGxD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;;kCACZ,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAIrC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;8BACb,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,YAAY;gCAAE,OAAO,QAAQ;4BAAI;4BACjC,WAAW,CAAC;;gBAEV,EAAE,eAAe,QAAQ,MAAM,EAAE;gBACjC,EAAE,iBAAiB,OAAO,QAAQ,EAAE,GAAG,0BAA0B,GAAG;gBACpE,EAAE,QAAQ,MAAM,KAAK,WAAW,oBAAoB,aAAa;cACnE,CAAC;4BACD,SAAS,IAAM,oBAAoB;;8CAEnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,QAAQ,MAAM;8DAC7B,8OAAC;oDAAK,WAAU;;wDAAiC;wDACtC,QAAQ;;;;;;;;;;;;;sDAGrB,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,MAAM,KAAK,0BAClB,8OAAC;gDAAK,WAAU;;kEACd,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,UAAU;;;;;;;;;;;;;;;;;;8CAM/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDAAe,QAAQ,aAAa;wDAAC;;;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDACb,QAAQ,gBAAgB;wDAAC;wDAAE,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;gCAKtD,QAAQ,MAAM,KAAK,eAAe,QAAQ,aAAa,GAAG,mBACzD,8OAAC;oCAAI,WAAU;;wCAA0C;wCACrD,QAAQ,aAAa;wCAAC;;;;;;;gCAI3B,QAAQ,MAAM,KAAK,0BAClB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;gDACL,OAAO,GAAG,KAAK,GAAG,CAAC,KAAK,AAAC,QAAQ,gBAAgB,GAAG,QAAQ,aAAa,GAAI,KAAK,CAAC,CAAC;4CACtF;;;;;;;;;;;;;;;;;2BAxDH,QAAQ,EAAE;;;;;;;;;;;;;;;YAkEtB,mBAAmB,gBAAgB,MAAM,KAAK,0BAC7C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;sCAAO;;;;;;wBAAwB;wBAAwB;wBACvD,gBAAgB,aAAa,GAAG,gBAAgB,gBAAgB;wBAAC;wBACjE,gBAAgB,gBAAgB,GAAG,gBAAgB,aAAa,kBAC/D,8OAAC;4BAAK,WAAU;;gCACb;gCAAI;;;;;;;;;;;;;;;;;;;;;;;;AAQrB", "debugId": null}}, {"offset": {"line": 1343, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/meme01/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport GameLayout from '@/components/GameLayout'\nimport WoodenFish from '@/components/WoodenFish'\nimport MissionPanel from '@/components/MissionPanel'\nimport { Mission } from '@/lib/missionUtils'\n\nexport default function Home() {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n  const [clickCount, setClickCount] = useState(0)\n  const [soundEnabled, setSoundEnabled] = useState(true)\n  const [currentMission, setCurrentMission] = useState<Mission | null>(null)\n  const [missionLoading, setMissionLoading] = useState(false)\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login')\n    }\n  }, [user, loading, router])\n\n  const handleFishClick = async (clickData: { x: number; y: number; timestamp: number }) => {\n    console.log('Fish clicked:', clickData)\n    setClickCount(prev => prev + 1)\n\n    // Send click to server for mission validation\n    if (currentMission && currentMission.status === 'active') {\n      setMissionLoading(true)\n\n      try {\n        const response = await fetch('/api/clicks', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            missionId: currentMission.id,\n            clickData\n          })\n        })\n\n        if (response.ok) {\n          const result = await response.json()\n          console.log('Click processed:', result)\n\n          // Update mission state will be handled by MissionPanel\n        } else {\n          console.error('Failed to process click:', await response.text())\n        }\n      } catch (error) {\n        console.error('Error processing click:', error)\n      } finally {\n        setMissionLoading(false)\n      }\n    }\n  }\n\n  const handleMissionSelect = (mission: Mission | null) => {\n    setCurrentMission(mission)\n    if (mission) {\n      setClickCount(mission.completed_clicks)\n    }\n  }\n\n  const handleMissionUpdate = (mission: Mission) => {\n    if (currentMission?.id === mission.id) {\n      setCurrentMission(mission)\n      setClickCount(mission.completed_clicks)\n    }\n  }\n\n\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">🪵🐟</div>\n          <div className=\"text-lg text-amber-800 font-medium\">Loading...</div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">🪵🐟</div>\n          <div className=\"text-lg text-amber-800 font-medium\">Redirecting to login...</div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <GameLayout user={user}>\n      <div className=\"flex-1 flex flex-col items-center justify-center p-4\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-amber-800 mb-2\">\n            木魚功德\n          </h1>\n          <p className=\"text-lg text-amber-600\">\n            Wooden Fish Merit Game\n          </p>\n        </div>\n\n        {/* Interactive Wooden Fish */}\n        <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-amber-200\">\n          <div className=\"flex justify-end mb-4\">\n            <button\n              onClick={() => setSoundEnabled(!soundEnabled)}\n              className={`\n                px-3 py-1 rounded-full text-sm font-medium transition-colors\n                ${soundEnabled\n                  ? 'bg-amber-100 text-amber-800 hover:bg-amber-200'\n                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                }\n              `}\n            >\n              {soundEnabled ? '🔊 Sound On' : '🔇 Sound Off'}\n            </button>\n          </div>\n\n          <WoodenFish\n            onFishClick={handleFishClick}\n            clickCount={clickCount}\n            soundEnabled={soundEnabled}\n            disabled={missionLoading || !currentMission || currentMission.status !== 'active'}\n          />\n        </div>\n\n        {/* Mission Panel */}\n        <div className=\"mt-8 w-full max-w-4xl\">\n          <MissionPanel\n            userId={user.id}\n            onMissionSelect={handleMissionSelect}\n            onMissionUpdate={handleMissionUpdate}\n          />\n        </div>\n\n        {/* Mission Status */}\n        {currentMission && (\n          <div className=\"mt-6 text-center\">\n            <div className=\"text-sm text-amber-700\">\n              <strong>Current Mission:</strong> {currentMission.completed_clicks}/{currentMission.target_clicks} clicks\n            </div>\n            {currentMission.completed_clicks >= currentMission.target_clicks && (\n              <div className=\"text-sm text-red-600 font-medium mt-1\">\n                ⚠️ Exceeding target will result in -100 penalty!\n              </div>\n            )}\n          </div>\n        )}\n\n        {!currentMission && (\n          <div className=\"mt-6 text-center text-amber-600\">\n            Select an active mission to start clicking\n          </div>\n        )}\n      </div>\n    </GameLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,MAAM,kBAAkB,OAAO;QAC7B,QAAQ,GAAG,CAAC,iBAAiB;QAC7B,cAAc,CAAA,OAAQ,OAAO;QAE7B,8CAA8C;QAC9C,IAAI,kBAAkB,eAAe,MAAM,KAAK,UAAU;YACxD,kBAAkB;YAElB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,eAAe;oBAC1C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,WAAW,eAAe,EAAE;wBAC5B;oBACF;gBACF;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,SAAS,MAAM,SAAS,IAAI;oBAClC,QAAQ,GAAG,CAAC,oBAAoB;gBAEhC,uDAAuD;gBACzD,OAAO;oBACL,QAAQ,KAAK,CAAC,4BAA4B,MAAM,SAAS,IAAI;gBAC/D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,SAAU;gBACR,kBAAkB;YACpB;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,IAAI,SAAS;YACX,cAAc,QAAQ,gBAAgB;QACxC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,gBAAgB,OAAO,QAAQ,EAAE,EAAE;YACrC,kBAAkB;YAClB,cAAc,QAAQ,gBAAgB;QACxC;IACF;IAIA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAI,WAAU;kCAAqC;;;;;;;;;;;;;;;;;IAI5D;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAI,WAAU;kCAAqC;;;;;;;;;;;;;;;;;IAI5D;IAEA,qBACE,8OAAC,gIAAA,CAAA,UAAU;QAAC,MAAM;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;8BAMxC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,gBAAgB,CAAC;gCAChC,WAAW,CAAC;;gBAEV,EAAE,eACE,mDACA,8CACH;cACH,CAAC;0CAEA,eAAe,gBAAgB;;;;;;;;;;;sCAIpC,8OAAC,gIAAA,CAAA,UAAU;4BACT,aAAa;4BACb,YAAY;4BACZ,cAAc;4BACd,UAAU,kBAAkB,CAAC,kBAAkB,eAAe,MAAM,KAAK;;;;;;;;;;;;8BAK7E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;wBACX,QAAQ,KAAK,EAAE;wBACf,iBAAiB;wBACjB,iBAAiB;;;;;;;;;;;gBAKpB,gCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAO;;;;;;gCAAyB;gCAAE,eAAe,gBAAgB;gCAAC;gCAAE,eAAe,aAAa;gCAAC;;;;;;;wBAEnG,eAAe,gBAAgB,IAAI,eAAe,aAAa,kBAC9D,8OAAC;4BAAI,WAAU;sCAAwC;;;;;;;;;;;;gBAO5D,CAAC,gCACA,8OAAC;oBAAI,WAAU;8BAAkC;;;;;;;;;;;;;;;;;AAO3D", "debugId": null}}]}