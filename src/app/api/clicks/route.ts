import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { validateClick } from '@/lib/missionUtils'

interface ClickRequest {
  missionId: string
  clickData: {
    x: number
    y: number
    timestamp: number
  }
}

// POST /api/clicks - Process a click for a mission
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient()
    
    // Get user from session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const body: ClickRequest = await request.json()

    if (!body.missionId || !body.clickData) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Get the mission
    const { data: mission, error: missionError } = await supabase
      .from('missions')
      .select('*')
      .eq('id', body.missionId)
      .eq('user_id', userId)
      .single()

    if (missionError || !mission) {
      return NextResponse.json({ error: 'Mission not found' }, { status: 404 })
    }

    // Anti-cheat: Check click timing (prevent too rapid clicks)
    const now = Date.now()
    const clickTime = body.clickData.timestamp
    const timeDiff = Math.abs(now - clickTime)
    
    if (timeDiff > 5000) { // Click timestamp too old (>5 seconds)
      return NextResponse.json({ error: 'Invalid click timestamp' }, { status: 400 })
    }

    // Log the click for anti-cheat analysis
    const { error: logError } = await supabase
      .from('click_logs')
      .insert({
        user_id: userId,
        mission_id: body.missionId,
        click_x: body.clickData.x,
        click_y: body.clickData.y,
        click_timestamp: new Date(clickTime).toISOString(),
        server_timestamp: new Date().toISOString()
      })

    if (logError) {
      console.warn('Failed to log click:', logError)
      // Continue processing even if logging fails
    }

    // Update mission click count
    const newClickCount = mission.completed_clicks + 1
    
    // Validate the click
    const validation = validateClick(mission, newClickCount)

    if (!validation.isValid) {
      return NextResponse.json({ 
        error: 'Invalid click',
        mission: mission,
        validation 
      }, { status: 400 })
    }

    // Update mission in database
    const { data: updatedMission, error: updateError } = await supabase
      .from('missions')
      .update({
        completed_clicks: newClickCount,
        status: validation.newStatus,
        score_awarded: validation.scoreAwarded,
        updated_at: new Date().toISOString()
      })
      .eq('id', body.missionId)
      .eq('user_id', userId)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating mission:', updateError)
      return NextResponse.json({ error: 'Failed to update mission' }, { status: 500 })
    }

    // If mission completed or failed, update user stats
    if (validation.newStatus === 'completed' || validation.newStatus === 'failed') {
      const scoreChange = validation.scoreAwarded + validation.penalty
      
      // Update user stats
      const { error: statsError } = await supabase.rpc('update_user_stats', {
        p_user_id: userId,
        p_score_change: scoreChange,
        p_mission_completed: validation.newStatus === 'completed' ? 1 : 0
      })

      if (statsError) {
        console.error('Error updating user stats:', statsError)
        // Don't fail the request if stats update fails
      }
    }

    return NextResponse.json({
      success: true,
      mission: updatedMission,
      validation: {
        isValid: validation.isValid,
        newStatus: validation.newStatus,
        penalty: validation.penalty,
        scoreAwarded: validation.scoreAwarded,
        clicksRemaining: Math.max(0, mission.target_clicks - newClickCount)
      }
    })

  } catch (error) {
    console.error('Click API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// GET /api/clicks - Get click statistics (for debugging)
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient()
    
    // Get user from session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError || !session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const url = new URL(request.url)
    const missionId = url.searchParams.get('missionId')

    if (!missionId) {
      return NextResponse.json({ error: 'Mission ID required' }, { status: 400 })
    }

    // Get click logs for the mission
    const { data: clickLogs, error: logsError } = await supabase
      .from('click_logs')
      .select('*')
      .eq('user_id', userId)
      .eq('mission_id', missionId)
      .order('server_timestamp', { ascending: true })

    if (logsError) {
      console.error('Error fetching click logs:', logsError)
      return NextResponse.json({ error: 'Failed to fetch click logs' }, { status: 500 })
    }

    return NextResponse.json({
      clickLogs: clickLogs || [],
      totalClicks: clickLogs?.length || 0
    })

  } catch (error) {
    console.error('Click GET API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
